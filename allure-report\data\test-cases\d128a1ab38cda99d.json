{"uid": "d128a1ab38cda99d", "name": "TC-01, <PERSON>gin to the URL", "fullName": "test.spec.ts#Login Tasks TC-01, Login to the URL", "historyId": "83e406c4843f1161094e4aa8d6cb36b7:84e28e814b821ed013329cc8dbc467e0", "time": {"start": 1746028224431, "stop": 1746028241781, "duration": 17350}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Before Hooks", "time": {"start": 1746028224437, "stop": 1746028234271, "duration": 9834}, "status": "passed", "steps": [{"name": "Create user", "time": {"start": 1746028224441, "stop": 1746028234271, "duration": 9830}, "status": "passed", "steps": [{"name": "fixture: browser", "time": {"start": 1746028224521, "stop": 1746028226829, "duration": 2308}, "status": "passed", "steps": [{"name": "browserType.launch", "time": {"start": 1746028224526, "stop": 1746028226829, "duration": 2303}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: context", "time": {"start": 1746028226832, "stop": 1746028226882, "duration": 50}, "status": "passed", "steps": [{"name": "browser.newContext", "time": {"start": 1746028226834, "stop": 1746028226879, "duration": 45}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: page", "time": {"start": 1746028226883, "stop": 1746028227760, "duration": 877}, "status": "passed", "steps": [{"name": "browserContext.newPage", "time": {"start": 1746028226887, "stop": 1746028227760, "duration": 873}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "page.goto(https://opensource-demo.orangehrmlive.com/web/index.php/auth/login)", "time": {"start": 1746028227820, "stop": 1746028234271, "duration": 6451}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 7, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 8, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746028234277, "stop": 1746028237217, "duration": 2940}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746028237219, "stop": 1746028237227, "duration": 8}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746028237229, "stop": 1746028237245, "duration": 16}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "locator.fill([name = 'username'])", "time": {"start": 1746028237249, "stop": 1746028237287, "duration": 38}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "locator.fill([name='password'])", "time": {"start": 1746028237289, "stop": 1746028237317, "duration": 28}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "locator.getByRole('button', { name: 'Login', exact: true }).click", "time": {"start": 1746028237319, "stop": 1746028238666, "duration": 1347}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "page.waitForTimeout", "time": {"start": 1746028238667, "stop": 1746028241670, "duration": 3003}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746028241672, "stop": 1746028241694, "duration": 22}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "After Hooks", "time": {"start": 1746028241694, "stop": 1746028241779, "duration": 85}, "status": "passed", "steps": [{"name": "Delete user", "time": {"start": 1746028241696, "stop": 1746028241697, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: page", "time": {"start": 1746028241697, "stop": 1746028241697, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: context", "time": {"start": 1746028241697, "stop": 1746028241697, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 3, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [{"uid": "a77ea20bd23eace7", "name": "stdout", "source": "a77ea20bd23eace7.txt", "type": "text/plain", "size": 73}], "parameters": [], "stepsCount": 21, "attachmentStep": false, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, "afterStages": [], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > webkit > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-13624-playwright-worker-2"}, {"name": "parentSuite", "value": "webkit"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "Project", "value": "webkit"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "d128a1ab38cda99d.json", "parameterValues": ["webkit"]}