name: Playwright Tests CI/CD

on:
  # Trigger on push to main/master branches
  push:
    branches: [ main, master ]
  
  # Trigger on all PR events (opened, synchronize, reopened)
  pull_request:
    branches: [ main, master ]
    types: [opened, synchronize, reopened]
  
  # Daily scheduled run at 1 PM PKT (8:00 AM UTC)
  schedule:
    - cron: '0 8 * * *'  # Daily at 8:00 AM UTC (1:00 PM PKT)
  
  # Allow manual workflow dispatch
  workflow_dispatch:

env:
  NODE_VERSION: '18'
  PLAYWRIGHT_BROWSERS_PATH: 0

jobs:
  test:
    name: Run Playwright Tests
    timeout-minutes: 60
    runs-on: ubuntu-latest
    
    strategy:
      fail-fast: false
      matrix:
        browser: [chromium]  # Can be extended to [chromium, firefox, webkit]
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          libnss3 \
          libnspr4 \
          libatk1.0-0 \
          libatk-bridge2.0-0 \
          libcups2 \
          libdrm2 \
          libxkbcommon0 \
          libatspi2.0-0 \
          libxcomposite1 \
          libxdamage1 \
          libxfixes3 \
          libxrandr2 \
          libgbm1 \
          libpango-1.0-0 \
          libcairo2 \
          libasound2t64
          
    - name: Install Playwright Browsers
      run: npx playwright install ${{ matrix.browser }}

      
    - name: Run Playwright tests
      run: npx playwright test --project=${{ matrix.browser }}
      env:
        CI: true
        
    - name: Generate Allure Report
      if: always()
      run: |
        npm run allure:generate
      continue-on-error: true
      
    - name: Upload Playwright Report
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-report-${{ matrix.browser }}-${{ github.run_number }}
        path: |
          playwright-report/
          test-results/
        retention-days: 30
        
    - name: Upload Allure Report
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: allure-report-${{ matrix.browser }}-${{ github.run_number }}
        path: allure-report/
        retention-days: 30
        
    - name: Upload Allure Results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: allure-results-${{ matrix.browser }}-${{ github.run_number }}
        path: allure-results/
        retention-days: 7

  # Job to post test results summary (optional)
  results-summary:
    name: Test Results Summary
    if: always()
    needs: test
    runs-on: ubuntu-latest
    
    steps:
    - name: Download all artifacts
      uses: actions/download-artifact@v4
      
    - name: Display test summary
      run: |
        echo "## Test Execution Summary" >> $GITHUB_STEP_SUMMARY
        echo "- **Trigger**: ${{ github.event_name }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Branch**: ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Run Number**: ${{ github.run_number }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Timestamp**: $(date -u)" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "Check the uploaded artifacts for detailed test reports and Allure results." >> $GITHUB_STEP_SUMMARY