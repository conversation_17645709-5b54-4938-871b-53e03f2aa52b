{"uuid": "92cd45a9-4c61-4cc1-bac3-ef5034b819d9", "historyId": "2c907fc9a8f1dc28613464738ffe75df:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "failed", "statusDetails": {"message": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: getByRole('heading', { name: 'Cockpit', exact: true })\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for getByRole('heading', { name: 'Cockpit', exact: true })\n", "trace": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: getByRole('heading', { name: 'Cockpit', exact: true })\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for getBy<PERSON>ole('heading', { name: 'Cockpit', exact: true })\n\n    at Object.enterLoginDetails (D:\\Playwright\\tests\\pages\\loginPage.ts:46:36)\n    at D:\\Playwright\\tests\\test.spec.ts:26:9"}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617523788, "name": "browserType.launch", "stop": 1746617524806}], "attachments": [], "parameters": [], "start": 1746617523784, "name": "fixture: browser", "stop": 1746617524806}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617524811, "name": "browser.newContext", "stop": 1746617524827}], "attachments": [], "parameters": [], "start": 1746617524809, "name": "fixture: context", "stop": 1746617524831}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617524835, "name": "browserContext.newPage", "stop": 1746617525206}], "attachments": [], "parameters": [], "start": 1746617524832, "name": "fixture: page", "stop": 1746617525206}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617525213, "name": "page.goto(https://opensource-demo.orangehrmlive.com/web/index.php/auth/login)", "stop": 1746617530316}], "attachments": [], "parameters": [], "start": 1746617523759, "name": "Create user", "stop": 1746617530316}], "attachments": [], "parameters": [], "start": 1746617523757, "name": "Before Hooks", "stop": 1746617530316}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617530320, "name": "expect.toBeVisible", "stop": 1746617533240}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617533241, "name": "expect.toBeVisible", "stop": 1746617533249}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617533250, "name": "expect.toBeVisible", "stop": 1746617533261}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617533263, "name": "locator.fill([name = 'username'])", "stop": 1746617533319}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617533321, "name": "locator.fill([name='password'])", "stop": 1746617533361}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617533363, "name": "locator.getByRole('button', { name: 'Login', exact: true }).click", "stop": 1746617534872}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617534874, "name": "page.waitForTimeout", "stop": 1746617537887}, {"status": "failed", "statusDetails": {"message": "Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: getByRole('heading', { name: 'Cockpit', exact: true })\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for getByRole('heading', { name: 'Cockpit', exact: true })\n", "trace": "\n    at Object.enterLoginDetails (D:\\Playwright\\tests\\pages\\loginPage.ts:46:36)\n    at D:\\Playwright\\tests\\test.spec.ts:26:9"}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617537888, "name": "expect.toBeVisible", "stop": 1746617542906}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617542906, "name": "Delete user", "stop": 1746617542924}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617542924, "name": "fixture: page", "stop": 1746617542924}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617542924, "name": "fixture: context", "stop": 1746617542924}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617542951, "name": "fixture: browser", "stop": 1746617543230}], "attachments": [], "parameters": [], "start": 1746617542906, "name": "After Hooks", "stop": 1746617543230}], "attachments": [{"name": "stdout", "type": "text/plain", "source": "61abb22c-5670-4bdb-a0c6-e18c9d39e9da-attachment.txt"}], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-17792-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}], "links": [], "start": 1746617523751, "name": "TC-02, Verify dashboard elements after login", "fullName": "test.spec.ts#Login Tasks TC-02, Verify dashboard elements after login", "testCaseId": "2c907fc9a8f1dc28613464738ffe75df", "stop": 1746617543235}