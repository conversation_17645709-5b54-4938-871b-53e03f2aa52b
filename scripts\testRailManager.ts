#!/usr/bin/env ts-node

/**
 * TestRail Management Script
 * 
 * This script provides command-line utilities for managing TestRail integration:
 * - Test connection to TestRail
 * - Create test runs
 * - Sync test cases
 * - View run results
 * - Manage test case mappings
 */

import * as dotenv from 'dotenv';
import { TestRailClient } from '../tests/utils/testRailClient';
import { TestRailIntegration } from '../tests/utils/testRailIntegration';
import { 
  defaultTestRailConfig, 
  defaultTestCaseMappings, 
  validateTestRailConfig,
  TestCaseMapping 
} from '../testrail.config';

// Load environment variables
dotenv.config();

class TestRailManager {
  private client?: TestRailClient;
  private integration?: TestRailIntegration;
  private configValid: boolean;

  constructor(skipValidation: boolean = false) {
    // Validate configuration
    const configErrors = validateTestRailConfig(defaultTestRailConfig);
    this.configValid = configErrors.length === 0;

    if (!this.configValid && !skipValidation) {
      console.error('❌ TestRail configuration errors:');
      configErrors.forEach(error => console.error(`  - ${error}`));
      console.error('\n💡 Run "npm run testrail help" for setup instructions');
      process.exit(1);
    }

    if (this.configValid) {
      this.client = new TestRailClient(defaultTestRailConfig);
      this.integration = new TestRailIntegration();
    }
  }

  /**
   * Test connection to TestRail
   */
  async testConnection(): Promise<void> {
    if (!this.client) {
      console.error('❌ TestRail client not initialized. Please check configuration.');
      return;
    }

    console.log('🔗 Testing TestRail connection...');

    try {
      const isConnected = await this.client.testConnection();
      if (isConnected) {
        console.log('✅ Connection successful!');
        
        // Get project info
        const project = await this.client.getProject();
        console.log(`📁 Project: ${project.name} (ID: ${project.id})`);
        console.log(`🔗 URL: ${project.url}`);
      } else {
        console.log('❌ Connection failed!');
        process.exit(1);
      }
    } catch (error) {
      console.error('❌ Connection error:', error);
      process.exit(1);
    }
  }

  /**
   * List test cases from the configured suite
   */
  async listTestCases(): Promise<void> {
    if (!this.client) {
      console.error('❌ TestRail client not initialized. Please check configuration.');
      return;
    }

    console.log('📋 Fetching test cases...');

    try {
      const cases = await this.client.getCases();
      console.log(`\n📊 Found ${cases.length} test cases in suite ${defaultTestRailConfig.suiteId}:\n`);
      
      cases.forEach(testCase => {
        console.log(`  🧪 Case ${testCase.id}: ${testCase.title}`);
        console.log(`     Priority: ${testCase.priority_id} | Type: ${testCase.type_id}`);
        if (testCase.refs) {
          console.log(`     References: ${testCase.refs}`);
        }
        console.log('');
      });
    } catch (error) {
      console.error('❌ Failed to fetch test cases:', error);
      process.exit(1);
    }
  }

  /**
   * Show current test case mappings
   */
  showMappings(): void {
    console.log('🗺️  Current test case mappings:\n');
    
    defaultTestCaseMappings.forEach(mapping => {
      console.log(`  📝 ${mapping.testId} → TestRail Case ${mapping.testRailCaseId}`);
      console.log(`     Title: ${mapping.title}`);
      console.log(`     Priority: ${mapping.priority} | Type: ${mapping.automationType}`);
      if (mapping.tags && mapping.tags.length > 0) {
        console.log(`     Tags: ${mapping.tags.join(', ')}`);
      }
      console.log('');
    });
    
    console.log(`📊 Total mappings: ${defaultTestCaseMappings.length}`);
  }

  /**
   * Create a new test run
   */
  async createTestRun(runName?: string, description?: string): Promise<void> {
    if (!this.client) {
      console.error('❌ TestRail client not initialized. Please check configuration.');
      return;
    }

    console.log('🚀 Creating new test run...');

    try {
      const name = runName || `Manual Test Run - ${new Date().toISOString().split('T')[0]}`;
      const desc = description || 'Manually created test run via TestRail Manager';

      // Get case IDs from mappings
      const caseIds = defaultTestCaseMappings.map(mapping => mapping.testRailCaseId);

      console.log(`📝 Run name: ${name}`);
      console.log(`📄 Description: ${desc}`);
      console.log(`🧪 Including ${caseIds.length} test cases`);

      const run = await this.client.createRun(name, desc, caseIds);
      
      console.log('\n✅ Test run created successfully!');
      console.log(`🆔 Run ID: ${run.id}`);
      console.log(`📛 Run Name: ${run.name}`);
      console.log(`🔗 URL: ${run.url}`);
      console.log(`📊 Tests: ${run.untested_count} untested`);
      
    } catch (error) {
      console.error('❌ Failed to create test run:', error);
      process.exit(1);
    }
  }

  /**
   * List recent test runs
   */
  async listRuns(): Promise<void> {
    console.log('📋 Fetching recent test runs...');
    
    try {
      // Note: This would require implementing get_runs in the client
      // For now, we'll show a placeholder
      console.log('ℹ️  Run listing feature coming soon...');
      console.log('💡 You can view runs in TestRail web interface:');
      console.log(`   ${defaultTestRailConfig.baseUrl}/index.php?/runs/overview/${defaultTestRailConfig.projectId}`);
      
    } catch (error) {
      console.error('❌ Failed to fetch runs:', error);
    }
  }

  /**
   * Validate test case mappings against TestRail
   */
  async validateMappings(): Promise<void> {
    if (!this.client) {
      console.error('❌ TestRail client not initialized. Please check configuration.');
      return;
    }

    console.log('🔍 Validating test case mappings...');

    try {
      const cases = await this.client.getCases();
      const testRailCaseIds = new Set(cases.map(c => c.id));
      
      console.log(`\n📊 Validation Results:\n`);
      
      let validCount = 0;
      let invalidCount = 0;
      
      for (const mapping of defaultTestCaseMappings) {
        if (testRailCaseIds.has(mapping.testRailCaseId)) {
          console.log(`✅ ${mapping.testId} → Case ${mapping.testRailCaseId} (Valid)`);
          validCount++;
        } else {
          console.log(`❌ ${mapping.testId} → Case ${mapping.testRailCaseId} (Invalid - Case not found)`);
          invalidCount++;
        }
      }
      
      console.log(`\n📈 Summary:`);
      console.log(`  ✅ Valid mappings: ${validCount}`);
      console.log(`  ❌ Invalid mappings: ${invalidCount}`);
      console.log(`  📊 Total mappings: ${defaultTestCaseMappings.length}`);
      
      if (invalidCount > 0) {
        console.log('\n⚠️  Please update invalid mappings in testrail.config.ts');
        process.exit(1);
      } else {
        console.log('\n🎉 All mappings are valid!');
      }
      
    } catch (error) {
      console.error('❌ Failed to validate mappings:', error);
      process.exit(1);
    }
  }

  /**
   * Show configuration summary
   */
  showConfig(): void {
    console.log('⚙️  TestRail Configuration:\n');
    
    console.log(`🌐 Base URL: ${defaultTestRailConfig.baseUrl}`);
    console.log(`👤 Username: ${defaultTestRailConfig.username}`);
    console.log(`🔑 API Key: ${'*'.repeat(defaultTestRailConfig.apiKey.length)}`);
    console.log(`📁 Project ID: ${defaultTestRailConfig.projectId}`);
    console.log(`📋 Suite ID: ${defaultTestRailConfig.suiteId}`);
    console.log(`🔄 Enabled: ${defaultTestRailConfig.enabled}`);
    console.log(`🚀 Auto Create Run: ${defaultTestRailConfig.createRunAutomatically}`);
    console.log(`📊 Auto Update Results: ${defaultTestRailConfig.updateResultsAutomatically}`);
    console.log(`📸 Include Screenshots: ${defaultTestRailConfig.includeScreenshots}`);
    console.log(`📝 Include Error Logs: ${defaultTestRailConfig.includeErrorLogs}`);
    
    if (defaultTestRailConfig.milestoneId) {
      console.log(`🎯 Milestone ID: ${defaultTestRailConfig.milestoneId}`);
    }
    
    if (defaultTestRailConfig.assignedToId) {
      console.log(`👥 Assigned To ID: ${defaultTestRailConfig.assignedToId}`);
    }
  }

  /**
   * Show help information
   */
  showHelp(): void {
    console.log(`
🧪 TestRail Manager - Command Line Interface

Usage: npm run testrail <command> [options]

Commands:
  test-connection     Test connection to TestRail
  list-cases         List all test cases from the configured suite
  show-mappings      Show current test case mappings
  create-run [name]  Create a new test run (optional name)
  list-runs          List recent test runs
  validate-mappings  Validate test case mappings against TestRail
  show-config        Show current configuration
  help               Show this help message

Examples:
  npm run testrail test-connection
  npm run testrail create-run "Sprint 1 Testing"
  npm run testrail validate-mappings

Configuration:
  Copy .env.example to .env and configure your TestRail settings.
  Update testrail.config.ts to customize test case mappings.

For more information, visit: https://github.com/your-repo/playwright-testrail
`);
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  if (!command || command === 'help') {
    new TestRailManager(true).showHelp();
    return;
  }

  // Commands that don't require TestRail connection
  const noConnectionCommands = ['show-mappings', 'show-config'];
  const skipValidation = noConnectionCommands.includes(command);

  const manager = new TestRailManager(skipValidation);
  
  try {
    switch (command) {
      case 'test-connection':
        await manager.testConnection();
        break;
        
      case 'list-cases':
        await manager.listTestCases();
        break;
        
      case 'show-mappings':
        manager.showMappings();
        break;
        
      case 'create-run':
        const runName = args[1];
        const description = args[2];
        await manager.createTestRun(runName, description);
        break;
        
      case 'list-runs':
        await manager.listRuns();
        break;
        
      case 'validate-mappings':
        await manager.validateMappings();
        break;
        
      case 'show-config':
        manager.showConfig();
        break;
        
      default:
        console.error(`❌ Unknown command: ${command}`);
        console.log('💡 Run "npm run testrail help" for available commands');
        process.exit(1);
    }
  } catch (error) {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { TestRailManager };
