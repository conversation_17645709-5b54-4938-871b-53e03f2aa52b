import { test } from '../baseFixture';
import { expect } from '@playwright/test';
import basePage from './pages/basePage';
import loginPage from './pages/loginPage';
import commonFunctions from './pages/commonFunctions';

test.beforeEach('Navigate to login page', async ({
    page,
}): Promise<void> => {
    console.log(`Setting up test: ${test.info().title}`);
    await basePage.loadUrl(page);
    await loginPage.seeTheLoginPage(page);
});

test.afterEach('Test cleanup', async (): Promise<void> => {
    console.log(`${test.info().title} ${test.info().status}`);
});

test.describe('Negative Login Test Cases', () => {
    
    test('TC-NEG-01: Login with invalid username and valid password', async ({ page }) => {
        await loginPage.enterInvalidLoginDetails(page, 'InvalidUser', 'admin123');
        await loginPage.verifyErrorMessage(page, 'invalid');
        
        // Take screenshot for evidence
        await page.screenshot({ path: 'screenshots/invalid-username.png' });
    });

    test('TC-NEG-02: Login with valid username and invalid password', async ({ page }) => {
        await loginPage.enterInvalidLoginDetails(page, 'Admin', 'wrongpassword');
        await loginPage.verifyErrorMessage(page, 'invalid');
        
        // Take screenshot for evidence
        await page.screenshot({ path: 'screenshots/invalid-password.png' });
    });

    test('TC-NEG-03: Login with both invalid username and password', async ({ page }) => {
        await loginPage.enterInvalidLoginDetails(page, 'InvalidUser', 'wrongpassword');
        await loginPage.verifyErrorMessage(page, 'invalid');
        
        // Take screenshot for evidence
        await page.screenshot({ path: 'screenshots/both-invalid.png' });
    });

    test('TC-NEG-04: Login with empty username field', async ({ page }) => {
        await loginPage.enterInvalidLoginDetails(page, '', 'admin123');
        
        // Check for required field validation or error message
        const elements = await loginPage.getLoginElements(page);
        const isUsernameRequired = await elements.emailField.getAttribute('required');
        
        if (isUsernameRequired) {
            console.log('Username field has required attribute');
            // Browser validation should prevent form submission
            expect(isUsernameRequired).not.toBeNull();
        } else {
            // Check for custom error message
            await loginPage.verifyErrorMessage(page);
        }
        
        await page.screenshot({ path: 'screenshots/empty-username.png' });
    });

    test('TC-NEG-05: Login with empty password field', async ({ page }) => {
        await loginPage.enterInvalidLoginDetails(page, 'Admin', '');
        
        // Check for required field validation or error message
        const elements = await loginPage.getLoginElements(page);
        const isPasswordRequired = await elements.passwordField.getAttribute('required');
        
        if (isPasswordRequired) {
            console.log('Password field has required attribute');
            expect(isPasswordRequired).not.toBeNull();
        } else {
            // Check for custom error message
            await loginPage.verifyErrorMessage(page);
        }
        
        await page.screenshot({ path: 'screenshots/empty-password.png' });
    });

    test('TC-NEG-06: Login with both empty fields', async ({ page }) => {
        await loginPage.enterInvalidLoginDetails(page, '', '');

        // Check for required field validation or error message
        const elements = await loginPage.getLoginElements(page);
        const isUsernameRequired = await elements.emailField.getAttribute('required');
        const isPasswordRequired = await elements.passwordField.getAttribute('required');

        if (isUsernameRequired || isPasswordRequired) {
            console.log('Form has required field validation');
            // Verify we're still on login page (form didn't submit)
            const loginButton = page.locator('button[type="submit"]');
            await expect(loginButton).toBeVisible();
        } else {
            // Check for custom error message or validation
            try {
                await loginPage.verifyErrorMessage(page);
            } catch (error) {
                // If no error message, verify we're still on login page
                const loginButton = page.locator('button[type="submit"]');
                await expect(loginButton).toBeVisible();
                console.log('No error message found, but form submission was prevented');
            }
        }

        await page.screenshot({ path: 'screenshots/both-empty.png' });
    });

    test('TC-NEG-07: SQL Injection attempt in username', async ({ page }) => {
        const sqlInjectionPayloads = [
            "' OR '1'='1",
            "admin'--",
            "' OR 1=1--",
            "admin'; DROP TABLE users;--"
        ];
        
        for (const payload of sqlInjectionPayloads) {
            console.log(`Testing SQL injection payload: ${payload}`);
            await loginPage.clearLoginFields(page);
            await loginPage.enterInvalidLoginDetails(page, payload, 'admin123');
            
            // Should not allow login and show error
            await loginPage.verifyErrorMessage(page);
            
            // Verify we're still on login page (not redirected to dashboard)
            const currentUrl = page.url();
            expect(currentUrl).toContain('login');
        }
        
        await page.screenshot({ path: 'screenshots/sql-injection-test.png' });
    });

    test('TC-NEG-08: Special characters in credentials', async ({ page }) => {
        const specialCharTests = [
            { username: 'Admin@#$%', password: 'admin123', description: 'Special chars in username' },
            { username: 'Admin', password: 'pass@#$%', description: 'Special chars in password' },
            { username: '<script>alert("xss")</script>', password: 'admin123', description: 'XSS attempt in username' },
            { username: 'Admin', password: '<script>alert("xss")</script>', description: 'XSS attempt in password' }
        ];
        
        for (const testCase of specialCharTests) {
            console.log(`Testing: ${testCase.description}`);
            await loginPage.clearLoginFields(page);
            await loginPage.enterInvalidLoginDetails(page, testCase.username, testCase.password);
            await loginPage.verifyErrorMessage(page);
            
            // Verify no XSS execution
            const pageContent = await page.content();
            expect(pageContent).not.toContain('<script>');
        }
        
        await page.screenshot({ path: 'screenshots/special-characters-test.png' });
    });

    test('TC-NEG-09: Very long username and password', async ({ page }) => {
        const longString = 'a'.repeat(1000); // 1000 character string
        
        await loginPage.enterInvalidLoginDetails(page, longString, 'admin123');
        await loginPage.verifyErrorMessage(page);
        
        await loginPage.clearLoginFields(page);
        await loginPage.enterInvalidLoginDetails(page, 'Admin', longString);
        await loginPage.verifyErrorMessage(page);
        
        await page.screenshot({ path: 'screenshots/long-credentials-test.png' });
    });

    test('TC-NEG-10: Case sensitivity test', async ({ page }) => {
        // Increase timeout for this test as it tests multiple scenarios
        test.setTimeout(60000);
        const caseSensitivityTests = [
            { username: 'admin', password: 'admin123', description: 'Lowercase username' },
            { username: 'ADMIN', password: 'admin123', description: 'Uppercase username' },
            { username: 'Admin', password: 'ADMIN123', description: 'Uppercase password' },
            { username: 'Admin', password: 'Admin123', description: 'Mixed case password' }
        ];

        for (const testCase of caseSensitivityTests) {
            console.log(`Testing case sensitivity: ${testCase.description}`);
            await loginPage.clearLoginFields(page);
            await loginPage.enterInvalidLoginDetails(page, testCase.username, testCase.password);

            // Wait for page to process the login attempt
            await page.waitForTimeout(3000);

            // Check if we're on dashboard (successful login) or still on login page
            const currentUrl = page.url();
            if (currentUrl.includes('dashboard')) {
                console.log(`${testCase.description}: Unexpectedly succeeded (case-insensitive)`);
                // Navigate back to login for next test
                await basePage.loadUrl(page);
                // Wait for page to fully load and ensure we're on login page
                await page.waitForLoadState('networkidle');
                await page.waitForTimeout(3000);

                // Verify we're back on login page without strict validation
                const usernameField = page.locator('[name="username"]');
                await usernameField.waitFor({ state: 'visible', timeout: 10000 });
                console.log('Successfully navigated back to login page');
            } else {
                // We're still on login page, try to verify error message
                try {
                    await loginPage.verifyErrorMessage(page);
                    console.log(`${testCase.description}: Failed as expected (case-sensitive)`);
                } catch (error) {
                    // If no error message but still on login page, that's also valid
                    const loginButton = page.locator('button[type="submit"]');
                    if (await loginButton.isVisible()) {
                        console.log(`${testCase.description}: Failed as expected (no error message but login prevented)`);
                    } else {
                        throw error;
                    }
                }
            }
        }

        await page.screenshot({ path: 'screenshots/case-sensitivity-test.png' });
    });

    test('TC-NEG-11: Multiple rapid login attempts', async ({ page }) => {
        // Increase timeout for this test as it makes multiple attempts
        test.setTimeout(60000);
        const attempts = 5;
        
        for (let i = 1; i <= attempts; i++) {
            console.log(`Login attempt ${i} of ${attempts}`);
            await loginPage.clearLoginFields(page);
            await loginPage.enterInvalidLoginDetails(page, 'InvalidUser', 'wrongpass');
            await loginPage.verifyErrorMessage(page);
            
            // Small delay between attempts
            await page.waitForTimeout(1000);
        }
        
        // Check if account gets locked or rate limiting is applied
        console.log('Checking for account lockout or rate limiting...');
        await loginPage.clearLoginFields(page);
        await loginPage.enterInvalidLoginDetails(page, 'Admin', 'admin123'); // Try valid credentials
        
        // This might fail if account is locked, or succeed if no rate limiting
        const currentUrl = page.url();
        if (currentUrl.includes('dashboard')) {
            console.log('No rate limiting detected - valid credentials still work');
        } else {
            console.log('Possible rate limiting or account lockout detected');
        }
        
        await page.screenshot({ path: 'screenshots/multiple-attempts-test.png' });
    });

    test('TC-NEG-12: Unicode and international characters', async ({ page }) => {
        // Increase timeout for this test as it tests multiple scenarios
        test.setTimeout(60000);
        const unicodeTests = [
            { username: 'Ädmin', password: 'admin123', description: 'Unicode in username' },
            { username: 'Admin', password: 'pässwörd', description: 'Unicode in password' },
            { username: '管理员', password: 'admin123', description: 'Chinese characters in username' },
            { username: 'Admin', password: 'пароль', description: 'Cyrillic in password' }
        ];

        for (const testCase of unicodeTests) {
            console.log(`Testing: ${testCase.description}`);
            await loginPage.clearLoginFields(page);
            await loginPage.enterInvalidLoginDetails(page, testCase.username, testCase.password);

            // Wait for page to process the login attempt
            await page.waitForTimeout(3000);

            // Check if we're on dashboard (successful login) or still on login page
            const currentUrl = page.url();
            if (currentUrl.includes('dashboard')) {
                console.log(`${testCase.description}: Unexpectedly succeeded`);
                // Navigate back to login for next test
                await basePage.loadUrl(page);
                // Wait for page to fully load and ensure we're on login page
                await page.waitForLoadState('networkidle');
                await page.waitForTimeout(3000);

                // Verify we're back on login page without strict validation
                const usernameField = page.locator('[name="username"]');
                await usernameField.waitFor({ state: 'visible', timeout: 10000 });
                console.log('Successfully navigated back to login page');
            } else {
                // We're still on login page, try to verify error message
                try {
                    await loginPage.verifyErrorMessage(page);
                    console.log(`${testCase.description}: Failed as expected`);
                } catch (error) {
                    // If no error message but still on login page, that's also valid
                    const loginButton = page.locator('button[type="submit"]');
                    if (await loginButton.isVisible()) {
                        console.log(`${testCase.description}: Failed as expected (no error message but login prevented)`);
                    } else {
                        throw error;
                    }
                }
            }
        }

        await page.screenshot({ path: 'screenshots/unicode-test.png' });
    });

});
