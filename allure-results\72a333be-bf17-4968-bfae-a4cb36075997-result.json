{"uuid": "72a333be-bf17-4968-bfae-a4cb36075997", "historyId": "2c907fc9a8f1dc28613464738ffe75df:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "failed", "statusDetails": {"message": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: locator('[name = \\'username\\']')\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('[name = \\'username\\']')\n", "trace": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: locator('[name = \\'username\\']')\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('[name = \\'username\\']')\n\n    at Object.seeTheLoginPage (D:\\Playwright\\tests\\pages\\loginPage.ts:23:34)\n    at D:\\Playwright\\tests\\test.spec.ts:25:25"}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617425495, "name": "browserType.launch", "stop": 1746617426505}], "attachments": [], "parameters": [], "start": 1746617425490, "name": "fixture: browser", "stop": 1746617426505}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617426510, "name": "browser.newContext", "stop": 1746617426530}], "attachments": [], "parameters": [], "start": 1746617426508, "name": "fixture: context", "stop": 1746617426534}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617426538, "name": "browserContext.newPage", "stop": 1746617426934}], "attachments": [], "parameters": [], "start": 1746617426535, "name": "fixture: page", "stop": 1746617426934}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617426939, "name": "page.goto(https://opensource-demo.orangehrmlive.com/web/index.php/auth/login)", "stop": 1746617435860}], "attachments": [], "parameters": [], "start": 1746617425467, "name": "Create user", "stop": 1746617435860}], "attachments": [], "parameters": [], "start": 1746617425466, "name": "Before Hooks", "stop": 1746617435861}, {"status": "failed", "statusDetails": {"message": "Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: locator('[name = \\'username\\']')\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('[name = \\'username\\']')\n", "trace": "\n    at Object.seeTheLoginPage (D:\\Playwright\\tests\\pages\\loginPage.ts:23:34)\n    at D:\\Playwright\\tests\\test.spec.ts:25:25"}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617435866, "name": "expect.toBeVisible", "stop": 1746617440874}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617440874, "name": "Delete user", "stop": 1746617440875}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617440875, "name": "fixture: page", "stop": 1746617440875}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617440876, "name": "fixture: context", "stop": 1746617440876}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617440917, "name": "fixture: browser", "stop": 1746617441117}], "attachments": [], "parameters": [], "start": 1746617440874, "name": "After Hooks", "stop": 1746617441117}], "attachments": [{"name": "stdout", "type": "text/plain", "source": "eeed8c15-9843-408f-8e7f-2b7f18c1eb74-attachment.txt"}], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-19640-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}], "links": [], "start": 1746617425460, "name": "TC-02, Verify dashboard elements after login", "fullName": "test.spec.ts#Login Tasks TC-02, Verify dashboard elements after login", "testCaseId": "2c907fc9a8f1dc28613464738ffe75df", "stop": 1746617441123}