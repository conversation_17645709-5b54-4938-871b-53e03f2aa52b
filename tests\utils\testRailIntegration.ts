/**
 * TestRail Integration Manager
 * 
 * This class manages the complete TestRail integration workflow:
 * - Test case mapping
 * - Test run creation
 * - Result synchronization
 * - Attachment handling
 */

import { TestInfo } from '@playwright/test';
import * as fs from 'fs';
import * as path from 'path';
import { TestRailClient, TestRailRun, TestRailTest } from './testRailClient';
import { 
  TestRailConfig, 
  TestCaseMapping, 
  TestRailStatus,
  defaultTestRailConfig,
  defaultTestCaseMappings,
  validateTestRailConfig
} from '../../testrail.config';

export interface TestResult {
  status: 'passed' | 'failed' | 'skipped' | 'timedOut';
  duration: number;
  error?: Error;
  startTime: Date;
  attachments: Array<{ path?: string; contentType?: string }>;
}

export interface TestExecutionResult {
  testId: string;
  testRailCaseId: number;
  status: 'passed' | 'failed' | 'skipped' | 'timedOut';
  duration: number;
  error?: string;
  screenshots?: string[];
  attachments?: string[];
  startTime: Date;
  endTime: Date;
}

export class TestRailIntegration {
  private client!: TestRailClient;
  private config: TestRailConfig;
  private testCaseMappings: Map<string, TestCaseMapping>;
  private currentRun: TestRailRun | null = null;
  private testResults: TestExecutionResult[] = [];
  private runTests: Map<number, TestRailTest> = new Map();

  constructor(config?: Partial<TestRailConfig>, customMappings?: TestCaseMapping[]) {
    // Merge provided config with defaults
    this.config = { ...defaultTestRailConfig, ...config };
    
    // Validate configuration
    const configErrors = validateTestRailConfig(this.config);
    if (configErrors.length > 0 && this.config.enabled) {
      console.error('[TestRail Integration] Configuration errors:');
      configErrors.forEach(error => console.error(`  - ${error}`));
      throw new Error('TestRail configuration is invalid');
    }

    // Initialize client if enabled
    if (this.config.enabled) {
      this.client = new TestRailClient(this.config);
    }

    // Initialize test case mappings
    this.testCaseMappings = new Map();
    const mappings = customMappings || defaultTestCaseMappings;
    mappings.forEach(mapping => {
      this.testCaseMappings.set(mapping.testId, mapping);
    });

    console.log(`[TestRail Integration] Initialized with ${this.testCaseMappings.size} test case mappings`);
    console.log(`[TestRail Integration] Enabled: ${this.config.enabled}`);
  }

  /**
   * Initialize TestRail integration before test execution
   */
  async initialize(): Promise<void> {
    if (!this.config.enabled) {
      console.log('[TestRail Integration] Disabled - skipping initialization');
      return;
    }

    try {
      console.log('[TestRail Integration] Initializing...');
      
      // Test connection
      const connectionOk = await this.client.testConnection();
      if (!connectionOk) {
        throw new Error('Failed to connect to TestRail');
      }

      // Get project info
      const project = await this.client.getProject();
      console.log(`[TestRail Integration] Connected to project: ${project.name} (ID: ${project.id})`);

      // Create test run if configured
      if (this.config.createRunAutomatically) {
        await this.createTestRun();
      }

      console.log('[TestRail Integration] Initialization complete');
    } catch (error) {
      console.error('[TestRail Integration] Initialization failed:', error);
      if (this.config.enabled) {
        throw error;
      }
    }
  }

  /**
   * Create a new test run with mapped test cases
   */
  async createTestRun(runName?: string, description?: string): Promise<TestRailRun | null> {
    if (!this.config.enabled) {
      return null;
    }

    try {
      // Get case IDs from mappings
      const caseIds = Array.from(this.testCaseMappings.values()).map(mapping => mapping.testRailCaseId);
      
      const name = runName || this.config.runName || `Automated Test Run - ${new Date().toISOString()}`;
      const desc = description || this.config.runDescription;

      console.log(`[TestRail Integration] Creating test run: ${name}`);
      console.log(`[TestRail Integration] Including ${caseIds.length} test cases`);

      this.currentRun = await this.client.createRun(name, desc, caseIds);
      
      // Get tests from the created run
      const tests = await this.client.getTests(this.currentRun.id);
      tests.forEach(test => {
        this.runTests.set(test.case_id, test);
      });

      console.log(`[TestRail Integration] Created run ${this.currentRun.id} with ${tests.length} tests`);
      return this.currentRun;
    } catch (error) {
      console.error('[TestRail Integration] Failed to create test run:', error);
      throw error;
    }
  }

  /**
   * Record test start
   */
  onTestStart(testInfo: TestInfo): void {
    if (!this.config.enabled) {
      return;
    }

    const testId = this.extractTestId(testInfo);
    if (!testId) {
      return;
    }

    console.log(`[TestRail Integration] Test started: ${testId} - ${testInfo.title}`);
  }

  /**
   * Record test completion and prepare result
   */
  onTestEnd(testInfo: TestInfo, result: TestResult): void {
    if (!this.config.enabled) {
      return;
    }

    const testId = this.extractTestId(testInfo);
    if (!testId) {
      return;
    }

    const mapping = this.testCaseMappings.get(testId);
    if (!mapping) {
      console.warn(`[TestRail Integration] No mapping found for test: ${testId}`);
      return;
    }

    // Collect screenshots and attachments
    const screenshots: string[] = [];
    const attachments: string[] = [];

    result.attachments.forEach((attachment: { path?: string; contentType?: string }) => {
      if (attachment.contentType?.startsWith('image/')) {
        screenshots.push(attachment.path || '');
      } else {
        attachments.push(attachment.path || '');
      }
    });

    // Create test execution result
    const testResult: TestExecutionResult = {
      testId: testId,
      testRailCaseId: mapping.testRailCaseId,
      status: result.status,
      duration: result.duration,
      error: result.error?.message,
      screenshots: screenshots.filter(path => path && fs.existsSync(path)),
      attachments: attachments.filter(path => path && fs.existsSync(path)),
      startTime: result.startTime,
      endTime: new Date(result.startTime.getTime() + result.duration)
    };

    this.testResults.push(testResult);
    console.log(`[TestRail Integration] Test completed: ${testId} - Status: ${result.status}`);
  }

  /**
   * Finalize and upload all results to TestRail
   */
  async finalizeResults(): Promise<void> {
    if (!this.config.enabled || !this.config.updateResultsAutomatically) {
      console.log('[TestRail Integration] Result upload disabled');
      return;
    }

    if (!this.currentRun) {
      console.warn('[TestRail Integration] No active test run - cannot upload results');
      return;
    }

    if (this.testResults.length === 0) {
      console.warn('[TestRail Integration] No test results to upload');
      return;
    }

    try {
      console.log(`[TestRail Integration] Uploading ${this.testResults.length} test results...`);

      for (const result of this.testResults) {
        await this.uploadTestResult(result);
      }

      // Generate summary
      const summary = this.generateResultSummary();
      console.log('[TestRail Integration] Results uploaded successfully');
      console.log(summary);

      // Optionally close the run
      if (process.env.TESTRAIL_CLOSE_RUN_AUTO === 'true') {
        await this.client.closeRun(this.currentRun.id);
        console.log(`[TestRail Integration] Test run ${this.currentRun.id} closed`);
      }

    } catch (error) {
      console.error('[TestRail Integration] Failed to upload results:', error);
      throw error;
    }
  }

  /**
   * Upload a single test result to TestRail
   */
  private async uploadTestResult(result: TestExecutionResult): Promise<void> {
    const runTest = this.runTests.get(result.testRailCaseId);
    if (!runTest) {
      console.warn(`[TestRail Integration] Test not found in run: Case ID ${result.testRailCaseId}`);
      return;
    }

    // Map Playwright status to TestRail status
    const statusId = this.mapStatusToTestRail(result.status);
    
    // Prepare comment
    let comment = `Automated test execution\n`;
    comment += `Duration: ${(result.duration / 1000).toFixed(2)}s\n`;
    comment += `Executed: ${result.endTime.toISOString()}\n`;
    
    if (result.error) {
      comment += `\nError:\n${result.error}`;
    }

    // Add result
    const elapsed = `${Math.ceil(result.duration / 1000)}s`;
    const testResultResponse = await this.client.addResult(runTest.id, statusId, comment, elapsed);

    // Upload attachments if enabled
    if (this.config.includeScreenshots && result.screenshots) {
      for (const screenshot of result.screenshots) {
        try {
          await this.client.addAttachment(testResultResponse.id, screenshot);
        } catch (error) {
          console.warn(`[TestRail Integration] Failed to upload screenshot: ${screenshot}`, error);
        }
      }
    }

    if (this.config.includeErrorLogs && result.attachments) {
      for (const attachment of result.attachments) {
        try {
          await this.client.addAttachment(testResultResponse.id, attachment);
        } catch (error) {
          console.warn(`[TestRail Integration] Failed to upload attachment: ${attachment}`, error);
        }
      }
    }

    console.log(`[TestRail Integration] Uploaded result for ${result.testId}: ${result.status}`);
  }

  /**
   * Extract test ID from test info
   */
  private extractTestId(testInfo: TestInfo): string | null {
    // Try to extract from test title (e.g., "TC-01: Basic login functionality")
    const titleMatch = testInfo.title.match(/^(TC-\w+(?:-\w+)*)/);
    if (titleMatch) {
      return titleMatch[1];
    }

    // Try to extract from test file name
    const fileMatch = testInfo.file.match(/(TC-\w+(?:-\w+)*)/);
    if (fileMatch) {
      return fileMatch[1];
    }

    // Try to extract from annotations
    const testIdAnnotation = testInfo.annotations.find(annotation => 
      annotation.type === 'testId' || annotation.type === 'test-id'
    );
    if (testIdAnnotation) {
      return testIdAnnotation.description || null;
    }

    return null;
  }

  /**
   * Map Playwright test status to TestRail status ID
   */
  private mapStatusToTestRail(status: string): number {
    switch (status) {
      case 'passed':
        return TestRailStatus.PASSED;
      case 'failed':
        return TestRailStatus.FAILED;
      case 'skipped':
        return TestRailStatus.SKIPPED || TestRailStatus.UNTESTED;
      case 'timedOut':
        return TestRailStatus.FAILED;
      default:
        return TestRailStatus.UNTESTED;
    }
  }

  /**
   * Generate a summary of test results
   */
  private generateResultSummary(): string {
    const total = this.testResults.length;
    const passed = this.testResults.filter(r => r.status === 'passed').length;
    const failed = this.testResults.filter(r => r.status === 'failed').length;
    const skipped = this.testResults.filter(r => r.status === 'skipped').length;
    const timedOut = this.testResults.filter(r => r.status === 'timedOut').length;

    const passRate = total > 0 ? ((passed / total) * 100).toFixed(1) : '0';

    return `
[TestRail Integration] Test Execution Summary:
  Total Tests: ${total}
  Passed: ${passed}
  Failed: ${failed}
  Skipped: ${skipped}
  Timed Out: ${timedOut}
  Pass Rate: ${passRate}%
  Run ID: ${this.currentRun?.id || 'N/A'}
  Run URL: ${this.currentRun?.url || 'N/A'}
`;
  }

  /**
   * Get current test run information
   */
  getCurrentRun(): TestRailRun | null {
    return this.currentRun;
  }

  /**
   * Get test case mappings
   */
  getTestCaseMappings(): Map<string, TestCaseMapping> {
    return this.testCaseMappings;
  }

  /**
   * Add or update a test case mapping
   */
  addTestCaseMapping(mapping: TestCaseMapping): void {
    this.testCaseMappings.set(mapping.testId, mapping);
    console.log(`[TestRail Integration] Added mapping: ${mapping.testId} -> ${mapping.testRailCaseId}`);
  }

  /**
   * Remove a test case mapping
   */
  removeTestCaseMapping(testId: string): boolean {
    const removed = this.testCaseMappings.delete(testId);
    if (removed) {
      console.log(`[TestRail Integration] Removed mapping: ${testId}`);
    }
    return removed;
  }
}
