{"uid": "3d19f1a7204fb669", "name": "TC-02, Verify dashboard elements after login", "fullName": "test.spec.ts#Login Tasks TC-02, Verify dashboard elements after login", "historyId": "2c907fc9a8f1dc28613464738ffe75df:5bd835b0d6b1d4ada3b9f0db936e82c8", "time": {"start": 1746617523751, "stop": 1746617543235, "duration": 19484}, "status": "failed", "statusMessage": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: getByRole('heading', { name: 'Cockpit', exact: true })\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for getByRole('heading', { name: 'Cockpit', exact: true })\n", "statusTrace": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: getByRole('heading', { name: 'Cockpit', exact: true })\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for getBy<PERSON>ole('heading', { name: 'Cockpit', exact: true })\n\n    at Object.enterLoginDetails (D:\\Playwright\\tests\\pages\\loginPage.ts:46:36)\n    at D:\\Playwright\\tests\\test.spec.ts:26:9", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "failed", "statusMessage": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: getByRole('heading', { name: 'Cockpit', exact: true })\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for getByRole('heading', { name: 'Cockpit', exact: true })\n", "statusTrace": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: getByRole('heading', { name: 'Cockpit', exact: true })\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for getBy<PERSON>ole('heading', { name: 'Cockpit', exact: true })\n\n    at Object.enterLoginDetails (D:\\Playwright\\tests\\pages\\loginPage.ts:46:36)\n    at D:\\Playwright\\tests\\test.spec.ts:26:9", "steps": [{"name": "Before Hooks", "time": {"start": 1746617523757, "stop": 1746617530316, "duration": 6559}, "status": "passed", "steps": [{"name": "Create user", "time": {"start": 1746617523759, "stop": 1746617530316, "duration": 6557}, "status": "passed", "steps": [{"name": "fixture: browser", "time": {"start": 1746617523784, "stop": 1746617524806, "duration": 1022}, "status": "passed", "steps": [{"name": "browserType.launch", "time": {"start": 1746617523788, "stop": 1746617524806, "duration": 1018}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: context", "time": {"start": 1746617524809, "stop": 1746617524831, "duration": 22}, "status": "passed", "steps": [{"name": "browser.newContext", "time": {"start": 1746617524811, "stop": 1746617524827, "duration": 16}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: page", "time": {"start": 1746617524832, "stop": 1746617525206, "duration": 374}, "status": "passed", "steps": [{"name": "browserContext.newPage", "time": {"start": 1746617524835, "stop": 1746617525206, "duration": 371}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "page.goto(https://opensource-demo.orangehrmlive.com/web/index.php/auth/login)", "time": {"start": 1746617525213, "stop": 1746617530316, "duration": 5103}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 7, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 8, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617530320, "stop": 1746617533240, "duration": 2920}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617533241, "stop": 1746617533249, "duration": 8}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617533250, "stop": 1746617533261, "duration": 11}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "locator.fill([name = 'username'])", "time": {"start": 1746617533263, "stop": 1746617533319, "duration": 56}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "locator.fill([name='password'])", "time": {"start": 1746617533321, "stop": 1746617533361, "duration": 40}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "locator.getByRole('button', { name: 'Login', exact: true }).click", "time": {"start": 1746617533363, "stop": 1746617534872, "duration": 1509}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "page.waitForTimeout", "time": {"start": 1746617534874, "stop": 1746617537887, "duration": 3013}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617537888, "stop": 1746617542906, "duration": 5018}, "status": "failed", "statusMessage": "Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: getByRole('heading', { name: 'Cockpit', exact: true })\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for getByRole('heading', { name: 'Cockpit', exact: true })\n", "statusTrace": "\n    at Object.enterLoginDetails (D:\\Playwright\\tests\\pages\\loginPage.ts:46:36)\n    at D:\\Playwright\\tests\\test.spec.ts:26:9", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": true}, {"name": "After Hooks", "time": {"start": 1746617542906, "stop": 1746617543230, "duration": 324}, "status": "passed", "steps": [{"name": "Delete user", "time": {"start": 1746617542906, "stop": 1746617542924, "duration": 18}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: page", "time": {"start": 1746617542924, "stop": 1746617542924, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: context", "time": {"start": 1746617542924, "stop": 1746617542924, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: browser", "time": {"start": 1746617542951, "stop": 1746617543230, "duration": 279}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 4, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [{"uid": "cb08340f251c29a4", "name": "stdout", "source": "cb08340f251c29a4.txt", "type": "text/plain", "size": 115}], "parameters": [], "stepsCount": 22, "attachmentStep": false, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": true}, "afterStages": [], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-17792-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "Project", "value": "chromium"}], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "3d19f1a7204fb669.json", "parameterValues": ["chromium"]}