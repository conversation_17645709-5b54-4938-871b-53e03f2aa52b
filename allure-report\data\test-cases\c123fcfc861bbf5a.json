{"uid": "c123fcfc861bbf5a", "name": "TC-02, Verify dashboard elements after login", "fullName": "test.spec.ts#Login Tasks TC-02, Verify dashboard elements after login", "historyId": "2c907fc9a8f1dc28613464738ffe75df:5bd835b0d6b1d4ada3b9f0db936e82c8", "time": {"start": 1746617689979, "stop": 1746617703187, "duration": 13208}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 3, "retriesStatusChange": true, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Before Hooks", "time": {"start": 1746617689980, "stop": 1746617695239, "duration": 5259}, "status": "passed", "steps": [{"name": "Create user", "time": {"start": 1746617689980, "stop": 1746617695239, "duration": 5259}, "status": "passed", "steps": [{"name": "fixture: context", "time": {"start": 1746617689997, "stop": 1746617690007, "duration": 10}, "status": "passed", "steps": [{"name": "browser.newContext", "time": {"start": 1746617689997, "stop": 1746617690005, "duration": 8}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: page", "time": {"start": 1746617690008, "stop": 1746617690223, "duration": 215}, "status": "passed", "steps": [{"name": "browserContext.newPage", "time": {"start": 1746617690010, "stop": 1746617690223, "duration": 213}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "page.goto(https://opensource-demo.orangehrmlive.com/web/index.php/auth/login)", "time": {"start": 1746617690225, "stop": 1746617695239, "duration": 5014}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 5, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 6, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617695241, "stop": 1746617698169, "duration": 2928}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617698169, "stop": 1746617698171, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617698172, "stop": 1746617698177, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "locator.fill([name = 'username'])", "time": {"start": 1746617698178, "stop": 1746617698193, "duration": 15}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "locator.fill([name='password'])", "time": {"start": 1746617698194, "stop": 1746617698207, "duration": 13}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "locator.getByRole('button', { name: 'Login', exact: true }).click", "time": {"start": 1746617698208, "stop": 1746617699929, "duration": 1721}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "page.waitForTimeout", "time": {"start": 1746617699930, "stop": 1746617702932, "duration": 3002}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617702933, "stop": 1746617702950, "duration": 17}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617702951, "stop": 1746617702956, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617702956, "stop": 1746617702959, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617702960, "stop": 1746617702962, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "page.screenshot", "time": {"start": 1746617702963, "stop": 1746617703144, "duration": 181}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "After Hooks", "time": {"start": 1746617703149, "stop": 1746617703184, "duration": 35}, "status": "passed", "steps": [{"name": "Delete user", "time": {"start": 1746617703149, "stop": 1746617703150, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: page", "time": {"start": 1746617703150, "stop": 1746617703150, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: context", "time": {"start": 1746617703150, "stop": 1746617703150, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 3, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [{"uid": "42e2075ee7b64267", "name": "stdout", "source": "42e2075ee7b64267.txt", "type": "text/plain", "size": 115}], "parameters": [], "stepsCount": 23, "attachmentStep": false, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, "afterStages": [], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-17532-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "Project", "value": "chromium"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "6ca33fc5d63e62df", "status": "failed", "statusDetails": "Error: expect.toBeVisible: Error: strict mode violation: locator('.oxd-main-menu') resolved to 2 elements:\n    1) <div data-v-636d6b87=\"\" class=\"oxd-main-menu --fixed\">…</div> aka getByLabel('Sidepanel').locator('div').filter({ hasText: 'AdminPIMLeaveTimeRecruitmentMy' }).locator('div').first()\n    2) <ul data-v-636d6b87=\"\" class=\"oxd-main-menu\">…</ul> aka getByText('AdminPIMLeaveTimeRecruitmentMy')\n\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('.oxd-main-menu')\n", "time": {"start": 1746617574933, "stop": 1746617589508, "duration": 14575}}, {"uid": "3d19f1a7204fb669", "status": "failed", "statusDetails": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: getByRole('heading', { name: 'Cockpit', exact: true })\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for getByRole('heading', { name: 'Cockpit', exact: true })\n", "time": {"start": 1746617523751, "stop": 1746617543235, "duration": 19484}}, {"uid": "2f4918ca54974d04", "status": "failed", "statusDetails": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: locator('[name = \\'username\\']')\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('[name = \\'username\\']')\n", "time": {"start": 1746617425460, "stop": 1746617441123, "duration": 15663}}], "categories": [], "tags": []}, "source": "c123fcfc861bbf5a.json", "parameterValues": ["chromium"]}