<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TestRail Integration Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
        }

        .section h2 {
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 20px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }

        .cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border-left: 5px solid #3498db;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            position: relative;
            padding-left: 25px;
        }

        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #27ae60;
            font-weight: bold;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .command-box {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
        }

        .command-box code {
            color: #f39c12;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-success { background-color: #27ae60; }
        .status-warning { background-color: #f39c12; }
        .status-error { background-color: #e74c3c; }
        .status-info { background-color: #3498db; }

        .config-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .config-table th,
        .config-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .config-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .config-table tr:hover {
            background-color: #f5f5f5;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.3s ease;
            margin: 5px;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        .workflow-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .step {
            text-align: center;
            flex: 1;
            min-width: 150px;
            margin: 10px;
        }

        .step-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 24px;
            font-weight: bold;
        }

        .step-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .step-desc {
            color: #666;
            font-size: 0.9em;
        }

        .arrow {
            color: #bdc3c7;
            font-size: 24px;
            margin: 0 10px;
        }

        @media (max-width: 768px) {
            .workflow-steps {
                flex-direction: column;
            }
            
            .arrow {
                transform: rotate(90deg);
                margin: 10px 0;
            }
            
            .cards {
                grid-template-columns: 1fr;
            }
        }

        .footer {
            background: #34495e;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 TestRail Integration Dashboard</h1>
            <p>Automated Test Case Management & Result Synchronization</p>
        </div>

        <div class="content">
            <!-- Overview Section -->
            <div class="section">
                <h2>📊 Integration Overview</h2>
                <div class="cards">
                    <div class="card">
                        <h3><span class="status-indicator status-success"></span>Automatic Test Runs</h3>
                        <p>Automatically creates TestRail test runs with mapped test cases before execution.</p>
                        <ul class="feature-list">
                            <li>Pre-configured test case mapping</li>
                            <li>Dynamic run creation</li>
                            <li>Milestone and assignment support</li>
                        </ul>
                    </div>
                    
                    <div class="card">
                        <h3><span class="status-indicator status-info"></span>Real-time Sync</h3>
                        <p>Synchronizes test results, screenshots, and logs with TestRail in real-time.</p>
                        <ul class="feature-list">
                            <li>Pass/Fail status updates</li>
                            <li>Screenshot attachments</li>
                            <li>Error log uploads</li>
                            <li>Execution time tracking</li>
                        </ul>
                    </div>
                    
                    <div class="card">
                        <h3><span class="status-indicator status-warning"></span>Management Tools</h3>
                        <p>Command-line tools for managing TestRail integration and test case mappings.</p>
                        <ul class="feature-list">
                            <li>Connection testing</li>
                            <li>Mapping validation</li>
                            <li>Run management</li>
                            <li>Configuration verification</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Workflow Section -->
            <div class="section">
                <h2>🔄 Integration Workflow</h2>
                <div class="workflow-steps">
                    <div class="step">
                        <div class="step-icon">1</div>
                        <div class="step-title">Initialize</div>
                        <div class="step-desc">Connect to TestRail and validate configuration</div>
                    </div>
                    <div class="arrow">→</div>
                    <div class="step">
                        <div class="step-icon">2</div>
                        <div class="step-title">Create Run</div>
                        <div class="step-desc">Automatically create test run with mapped cases</div>
                    </div>
                    <div class="arrow">→</div>
                    <div class="step">
                        <div class="step-icon">3</div>
                        <div class="step-title">Execute</div>
                        <div class="step-desc">Run Playwright tests with TestRail hooks</div>
                    </div>
                    <div class="arrow">→</div>
                    <div class="step">
                        <div class="step-icon">4</div>
                        <div class="step-title">Sync Results</div>
                        <div class="step-desc">Upload results and attachments to TestRail</div>
                    </div>
                </div>
            </div>

            <!-- Quick Start Section -->
            <div class="section">
                <h2>🚀 Quick Start Commands</h2>
                <div class="cards">
                    <div class="card">
                        <h3>Test Connection</h3>
                        <p>Verify your TestRail connection and configuration:</p>
                        <div class="command-box">
                            <code>npm run testrail:test-connection</code>
                        </div>
                        <button class="btn btn-success" onclick="copyToClipboard('npm run testrail:test-connection')">Copy Command</button>
                    </div>
                    
                    <div class="card">
                        <h3>Run Tests with TestRail</h3>
                        <p>Execute your Playwright tests with TestRail integration:</p>
                        <div class="command-box">
                            <code>npm run test:testrail</code>
                        </div>
                        <button class="btn btn-success" onclick="copyToClipboard('npm run test:testrail')">Copy Command</button>
                    </div>
                    
                    <div class="card">
                        <h3>Validate Mappings</h3>
                        <p>Ensure all test case mappings are valid:</p>
                        <div class="command-box">
                            <code>npm run testrail:validate</code>
                        </div>
                        <button class="btn btn-warning" onclick="copyToClipboard('npm run testrail:validate')">Copy Command</button>
                    </div>
                    
                    <div class="card">
                        <h3>Create Test Run</h3>
                        <p>Manually create a new TestRail test run:</p>
                        <div class="command-box">
                            <code>npm run testrail:create-run</code>
                        </div>
                        <button class="btn btn-info" onclick="copyToClipboard('npm run testrail:create-run')">Copy Command</button>
                    </div>
                </div>
            </div>

            <!-- Configuration Section -->
            <div class="section">
                <h2>⚙️ Configuration Settings</h2>
                <p>Configure your TestRail integration by setting these environment variables in your <code>.env</code> file:</p>
                
                <table class="config-table">
                    <thead>
                        <tr>
                            <th>Setting</th>
                            <th>Environment Variable</th>
                            <th>Required</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>TestRail URL</td>
                            <td><code>TESTRAIL_BASE_URL</code></td>
                            <td><span class="status-indicator status-error"></span>Yes</td>
                            <td>Your TestRail instance URL</td>
                        </tr>
                        <tr>
                            <td>Username</td>
                            <td><code>TESTRAIL_USERNAME</code></td>
                            <td><span class="status-indicator status-error"></span>Yes</td>
                            <td>TestRail username/email</td>
                        </tr>
                        <tr>
                            <td>API Key</td>
                            <td><code>TESTRAIL_API_KEY</code></td>
                            <td><span class="status-indicator status-error"></span>Yes</td>
                            <td>TestRail API key</td>
                        </tr>
                        <tr>
                            <td>Project ID</td>
                            <td><code>TESTRAIL_PROJECT_ID</code></td>
                            <td><span class="status-indicator status-error"></span>Yes</td>
                            <td>TestRail project ID</td>
                        </tr>
                        <tr>
                            <td>Suite ID</td>
                            <td><code>TESTRAIL_SUITE_ID</code></td>
                            <td><span class="status-indicator status-error"></span>Yes</td>
                            <td>TestRail test suite ID</td>
                        </tr>
                        <tr>
                            <td>Enable Integration</td>
                            <td><code>TESTRAIL_ENABLED</code></td>
                            <td><span class="status-indicator status-warning"></span>Optional</td>
                            <td>Enable/disable integration (default: false)</td>
                        </tr>
                        <tr>
                            <td>Include Screenshots</td>
                            <td><code>TESTRAIL_INCLUDE_SCREENSHOTS</code></td>
                            <td><span class="status-indicator status-warning"></span>Optional</td>
                            <td>Upload screenshots (default: true)</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Test Case Mapping Section -->
            <div class="section">
                <h2>🗺️ Test Case Mapping</h2>
                <div class="cards">
                    <div class="card">
                        <h3>Current Mappings</h3>
                        <p>Your project currently has <strong>15 test cases</strong> mapped to TestRail:</p>
                        <ul class="feature-list">
                            <li>TC-01 → TestRail Case 1001 (Basic login)</li>
                            <li>TC-02 → TestRail Case 1002 (Dashboard verification)</li>
                            <li>TC-NEG-01 to TC-NEG-12 → Cases 2001-2012 (Negative tests)</li>
                        </ul>
                        <button class="btn" onclick="showMappings()">View All Mappings</button>
                    </div>
                    
                    <div class="card">
                        <h3>Mapping Guidelines</h3>
                        <p>Follow these conventions for automatic test case detection:</p>
                        <div class="command-box">
test('TC-01: Basic login functionality', async ({ page }) => {
  // Test implementation
});
                        </div>
                        <p>The integration will automatically extract <code>TC-01</code> and map it to the configured TestRail case.</p>
                    </div>
                </div>
            </div>

            <!-- Status Section -->
            <div class="section">
                <h2>📈 Integration Status</h2>
                <div class="cards">
                    <div class="card">
                        <h3><span class="status-indicator status-success"></span>System Status</h3>
                        <p><strong>Integration:</strong> Ready</p>
                        <p><strong>Configuration:</strong> Complete</p>
                        <p><strong>Test Mappings:</strong> 15 mapped cases</p>
                        <p><strong>Last Run:</strong> Not executed yet</p>
                    </div>
                    
                    <div class="card">
                        <h3>Next Steps</h3>
                        <ol style="padding-left: 20px;">
                            <li>Configure your <code>.env</code> file with TestRail credentials</li>
                            <li>Test the connection: <code>npm run testrail:test-connection</code></li>
                            <li>Validate mappings: <code>npm run testrail:validate</code></li>
                            <li>Run your first integrated test: <code>npm run test:testrail</code></li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>🧪 TestRail Integration Dashboard | Built for Playwright Test Automation</p>
            <p>For detailed documentation, see <strong>TESTRAIL_INTEGRATION.md</strong></p>
        </div>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Create a temporary notification
                const notification = document.createElement('div');
                notification.textContent = 'Command copied to clipboard!';
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #27ae60;
                    color: white;
                    padding: 10px 20px;
                    border-radius: 5px;
                    z-index: 1000;
                    font-weight: bold;
                `;
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 2000);
            });
        }

        function showMappings() {
            alert('To view detailed mappings, check the testrail.config.ts file or run: npm run testrail show-mappings');
        }
    </script>
</body>
</html>
