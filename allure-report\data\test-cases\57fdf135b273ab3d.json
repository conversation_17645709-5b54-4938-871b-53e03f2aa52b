{"uid": "57fdf135b273ab3d", "name": "TC-01, <PERSON>gin to the URL", "fullName": "test.spec.ts#Login Tasks TC-01, Login to the URL", "historyId": "83e406c4843f1161094e4aa8d6cb36b7:5bd835b0d6b1d4ada3b9f0db936e82c8", "time": {"start": 1746617674735, "stop": 1746617689973, "duration": 15238}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 5, "retriesStatusChange": true, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Before Hooks", "time": {"start": 1746617674738, "stop": 1746617682174, "duration": 7436}, "status": "passed", "steps": [{"name": "Create user", "time": {"start": 1746617674740, "stop": 1746617682173, "duration": 7433}, "status": "passed", "steps": [{"name": "fixture: browser", "time": {"start": 1746617674765, "stop": 1746617675664, "duration": 899}, "status": "passed", "steps": [{"name": "browserType.launch", "time": {"start": 1746617674768, "stop": 1746617675664, "duration": 896}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: context", "time": {"start": 1746617675666, "stop": 1746617675689, "duration": 23}, "status": "passed", "steps": [{"name": "browser.newContext", "time": {"start": 1746617675669, "stop": 1746617675686, "duration": 17}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: page", "time": {"start": 1746617675691, "stop": 1746617676100, "duration": 409}, "status": "passed", "steps": [{"name": "browserContext.newPage", "time": {"start": 1746617675694, "stop": 1746617676100, "duration": 406}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "page.goto(https://opensource-demo.orangehrmlive.com/web/index.php/auth/login)", "time": {"start": 1746617676105, "stop": 1746617682173, "duration": 6068}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 7, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 8, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617682179, "stop": 1746617685131, "duration": 2952}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617685133, "stop": 1746617685142, "duration": 9}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617685144, "stop": 1746617685155, "duration": 11}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "locator.fill([name = 'username'])", "time": {"start": 1746617685158, "stop": 1746617685180, "duration": 22}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "locator.fill([name='password'])", "time": {"start": 1746617685181, "stop": 1746617685207, "duration": 26}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "locator.getByRole('button', { name: 'Login', exact: true }).click", "time": {"start": 1746617685208, "stop": 1746617686899, "duration": 1691}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "page.waitForTimeout", "time": {"start": 1746617686901, "stop": 1746617689903, "duration": 3002}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617689904, "stop": 1746617689922, "duration": 18}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "After Hooks", "time": {"start": 1746617689922, "stop": 1746617689968, "duration": 46}, "status": "passed", "steps": [{"name": "Delete user", "time": {"start": 1746617689924, "stop": 1746617689926, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: page", "time": {"start": 1746617689927, "stop": 1746617689927, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: context", "time": {"start": 1746617689928, "stop": 1746617689928, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 3, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [{"uid": "dcefde933aae15e", "name": "stdout", "source": "dcefde933aae15e.txt", "type": "text/plain", "size": 73}], "parameters": [], "stepsCount": 21, "attachmentStep": false, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, "afterStages": [], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-17532-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "Project", "value": "chromium"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "5cf8a094c1c98d17", "status": "passed", "time": {"start": 1746617558878, "stop": 1746617574929, "duration": 16051}}, {"uid": "345aedcb9097894e", "status": "failed", "statusDetails": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: getByRole('heading', { name: 'Cockpit', exact: true })\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for getByRole('heading', { name: 'Cockpit', exact: true })\n", "time": {"start": 1746617500593, "stop": 1746617523319, "duration": 22726}}, {"uid": "804cac490086c202", "status": "failed", "statusDetails": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: locator('[name = \\'username\\']')\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('[name = \\'username\\']')\n", "time": {"start": 1746617403244, "stop": 1746617425019, "duration": 21775}}, {"uid": "e7a0fba391fccfb9", "status": "failed", "time": {"start": 1746028296353, "stop": 1746028297117, "duration": 764}}, {"uid": "cc89684592f342c1", "status": "passed", "time": {"start": 1746028224376, "stop": 1746028241665, "duration": 17289}}], "categories": [], "tags": []}, "source": "57fdf135b273ab3d.json", "parameterValues": ["chromium"]}