{"uuid": "8a4d4d40-0c34-4097-bfc5-624d08a44d57", "historyId": "83e406c4843f1161094e4aa8d6cb36b7:b444eb0fbe6390c71e68b51dd25701fc", "status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028224459, "name": "browserType.launch", "stop": 1746028227501}], "attachments": [], "parameters": [], "start": 1746028224452, "name": "fixture: browser", "stop": 1746028227501}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028227501, "name": "browser.newContext", "stop": 1746028227501}], "attachments": [], "parameters": [], "start": 1746028227501, "name": "fixture: context", "stop": 1746028227502}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028227502, "name": "browserContext.newPage", "stop": 1746028227935}], "attachments": [], "parameters": [], "start": 1746028227502, "name": "fixture: page", "stop": 1746028227935}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028227960, "name": "page.goto(https://opensource-demo.orangehrmlive.com/web/index.php/auth/login)", "stop": 1746028234259}], "attachments": [], "parameters": [], "start": 1746028224414, "name": "Create user", "stop": 1746028234259}], "attachments": [], "parameters": [], "start": 1746028224411, "name": "Before Hooks", "stop": 1746028234259}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028234269, "name": "expect.toBeVisible", "stop": 1746028237235}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028237238, "name": "expect.toBeVisible", "stop": 1746028237246}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028237248, "name": "expect.toBeVisible", "stop": 1746028237261}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028237268, "name": "locator.fill([name = 'username'])", "stop": 1746028237307}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028237309, "name": "locator.fill([name='password'])", "stop": 1746028237332}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028237333, "name": "locator.getByRole('button', { name: 'Login', exact: true }).click", "stop": 1746028238680}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028238682, "name": "page.waitForTimeout", "stop": 1746028241687}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028241688, "name": "expect.toBeVisible", "stop": 1746028241716}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028241723, "name": "Delete user", "stop": 1746028241783}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028241783, "name": "fixture: page", "stop": 1746028241783}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028241783, "name": "fixture: context", "stop": 1746028241783}], "attachments": [], "parameters": [], "start": 1746028241721, "name": "After Hooks", "stop": 1746028241844}], "attachments": [{"name": "stdout", "type": "text/plain", "source": "3523e6e4-494c-4367-b7dd-506bc4f2acf6-attachment.txt"}], "parameters": [{"name": "Project", "value": "firefox"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > firefox > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-13624-playwright-worker-1"}, {"name": "parentSuite", "value": "firefox"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}], "links": [], "start": 1746028224404, "name": "TC-01, <PERSON>gin to the URL", "fullName": "test.spec.ts#Login Tasks TC-01, Login to the URL", "testCaseId": "83e406c4843f1161094e4aa8d6cb36b7", "stop": 1746028241845}