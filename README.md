# Playwright Augment Code - Test Automation Framework

A comprehensive Playwright test automation framework with TypeScript, featuring AI-enhanced testing capabilities, Page Object Model architecture, and Allure reporting.

## 🚀 Features

- **Playwright TypeScript Framework** - Modern web automation testing
- **AI-Enhanced Testing** - Integration with @zerostep/playwright for intelligent test automation
- **Page Object Model** - Organized and maintainable test structure
- **Common Functions Library** - Reusable utility functions for web interactions
- **Allure Reporting** - Beautiful and detailed test reports
- **Cross-Browser Testing** - Support for Chromium, Firefox, and WebKit
- **Mobile Testing** - Mobile device testing capabilities

## 📋 Prerequisites

Before setting up this project, ensure you have the following installed:

- **Node.js** (version 16 or higher)
- **npm** (comes with Node.js)
- **Git** (for version control)

## 🛠️ Setup Instructions

### 1. Clone the Repository

```bash
git clone https://github.com/tufailkhan2/PlaywrightAugmentCode.git
cd PlaywrightAugmentCode
```

### 2. Install Dependencies

```bash
npm install
```

This will install all required dependencies including:
- Playwright Test Framework
- TypeScript support
- Allure reporting tools
- AI testing capabilities

### 3. Install Playwright Browsers

```bash
npx playwright install
```

This downloads the necessary browser binaries (Chromium, Firefox, WebKit).

### 4. Verify Installation

```bash
npm test
```

## 🏃‍♂️ Running Tests

### Run All Tests
```bash
npm test
```

### Run Tests in Headed Mode (Browser Visible)
```bash
npx playwright test --headed
```

### Run Specific Test File
```bash
npx playwright test tests/test.spec.ts
```

### Run Tests in Debug Mode
```bash
npx playwright test --debug
```

### Run Mobile Tests
```bash
npx playwright test tests/mobile.spec.ts
```

## 📊 Test Reporting

### Generate Allure Report
```bash
npm run allure:generate
```

### Open Allure Report
```bash
npm run allure:open
```

### View HTML Report (Default Playwright Report)
```bash
npx playwright show-report
```

## 📁 Project Structure

```
PlaywrightAugmentCode/
├── tests/
│   ├── pages/
│   │   ├── basePage.ts          # Base page class
│   │   ├── commonFunctions.ts   # Reusable utility functions
│   │   └── loginPage.ts         # Login page object
│   ├── test.spec.ts             # Main test specifications
│   └── mobile.spec.ts           # Mobile testing specifications
├── allure-results/              # Allure test results
├── allure-report/               # Generated Allure reports
├── baseFixture.ts               # AI-enhanced test fixtures
├── playwright.config.ts         # Playwright configuration
├── package.json                 # Project dependencies and scripts
└── README.md                    # This file
```

## 🔧 Configuration

### Playwright Configuration (`playwright.config.ts`)
- **Test Directory**: `./tests`
- **Reporter**: Allure + Line reporter
- **Browser**: Chromium (maximized, non-headless by default)
- **Viewport**: Full screen

### Common Functions (`tests/pages/commonFunctions.ts`)
The project includes a comprehensive library of reusable functions:

#### Text Operations
- `readText()` - Read text content from elements
- `readTextContent()` - Alternative text reading method
- `readInnerText()` - Read inner text from elements

#### Input Operations
- `enterText()` - Enter text into input fields
- `clearAndEnterText()` - Clear and enter new text

#### Click Operations
- `clickElement()` - Standard click
- `doubleClickElement()` - Double click
- `rightClickElement()` - Right click

#### Scroll Operations
- `scrollToElement()` - Scroll to make element visible
- `scrollDown()` / `scrollUp()` - Page scrolling

#### Wait Operations
- `waitForElementVisible()` - Wait for element visibility
- `waitForElementHidden()` - Wait for element to hide

#### Utility Functions
- `isElementVisible()` - Check element visibility
- `isElementEnabled()` - Check if element is enabled
- `getElementCount()` - Count matching elements
- `takeScreenshot()` - Capture screenshots
- `checkAccessibility()` - Basic accessibility checks
- `measurePageLoad()` - Performance monitoring

## 🧪 Writing Tests

### Basic Test Structure
```typescript
import { test, expect } from '@playwright/test';
import commonFunctions from './pages/commonFunctions';

test('Example test', async ({ page }) => {
    await page.goto('https://example.com');
    
    // Use common functions
    await commonFunctions.enterText(page, '#username', 'testuser');
    await commonFunctions.clickElement(page, '#login-button');
    await commonFunctions.waitForElementVisible(page, '.dashboard');
});
```

### Using Page Objects
```typescript
import basePage from './pages/basePage';
import loginPage from './pages/loginPage';

test('Login test', async ({ page }) => {
    await basePage.loadUrl(page);
    await loginPage.seeTheLoginPage(page);
    await loginPage.enterLoginDetails(page);
});
```

## 🤖 AI-Enhanced Testing

This project includes AI testing capabilities through `@zerostep/playwright`. The base fixture in `baseFixture.ts` provides enhanced testing features for more intelligent automation.

## 🐛 Debugging

### Debug Mode
```bash
npx playwright test --debug
```

### Trace Viewer
```bash
npx playwright test --trace on
npx playwright show-trace trace.zip
```

### Screenshots
Screenshots are automatically captured and stored in the project directory.

## 📱 Mobile Testing

The project includes mobile testing capabilities. Configure device emulation in `playwright.config.ts` or use the existing mobile test specifications.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📝 Available Scripts

- `npm test` - Run all tests
- `npm run allure:generate` - Generate Allure report
- `npm run allure:open` - Open Allure report in browser

## 🔍 Troubleshooting

### Common Issues

1. **Browser not found**: Run `npx playwright install`
2. **Tests failing**: Check if the application URL is accessible
3. **Allure reports not generating**: Ensure `allure-commandline` is installed

### Getting Help

- Check Playwright documentation: https://playwright.dev/
- Review test logs in the console output
- Use debug mode for step-by-step execution

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

---

**Happy Testing! 🎭**
