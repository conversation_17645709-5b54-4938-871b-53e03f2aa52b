{"uuid": "967fa12d-3716-49a7-9e91-045ef7a7fa08", "historyId": "0e8b58035f0104d4325344b3aeb04fe2:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "failed", "statusDetails": {"message": "ReferenceError: context is not defined", "trace": "ReferenceError: context is not defined\n    at D:\\Playwright\\tests\\mobile.spec.ts:42:5"}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746692208735, "name": "Before Hooks", "stop": 1746692208754}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746692208756, "name": "android.devices", "stop": 1746692208789}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746692208791, "name": "androidDevice.screenshot", "stop": 1746692209319}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746692209323, "name": "androidDevice.shell", "stop": 1746692209352}, {"status": "failed", "statusDetails": {"message": "ReferenceError: context is not defined", "trace": "ReferenceError: context is not defined\n    at D:\\Playwright\\tests\\mobile.spec.ts:42:5"}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746692209354, "name": "ReferenceError: context is not defined", "stop": 1746692209357}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746692209357, "name": "After Hooks", "stop": 1746692209357}], "attachments": [{"name": "stdout", "type": "text/plain", "source": "f867002f-cf71-4a4e-8f14-43ee572ef6d5-attachment.txt"}], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > mobile.spec.ts"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-22192-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "mobile.spec.ts"}], "links": [], "start": 1746692208730, "name": "Run in Android - Chrome", "fullName": "mobile.spec.ts#Run in Android - Chrome", "testCaseId": "0e8b58035f0104d4325344b3aeb04fe2", "stop": 1746692209362}