{"uuid": "e3106967-8ced-4759-a3e4-b63daf5dabae", "historyId": "83e406c4843f1161094e4aa8d6cb36b7:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "failed", "statusDetails": {"message": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: getByRole('heading', { name: 'Cockpit', exact: true })\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for getByRole('heading', { name: 'Cockpit', exact: true })\n", "trace": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: getByRole('heading', { name: 'Cockpit', exact: true })\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for getBy<PERSON>ole('heading', { name: 'Cockpit', exact: true })\n\n    at Object.enterLoginDetails (D:\\Playwright\\tests\\pages\\loginPage.ts:46:36)\n    at D:\\Playwright\\tests\\test.spec.ts:20:9"}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617500628, "name": "browserType.launch", "stop": 1746617501586}], "attachments": [], "parameters": [], "start": 1746617500625, "name": "fixture: browser", "stop": 1746617501586}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617501591, "name": "browser.newContext", "stop": 1746617501609}], "attachments": [], "parameters": [], "start": 1746617501589, "name": "fixture: context", "stop": 1746617501616}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617501620, "name": "browserContext.newPage", "stop": 1746617502044}], "attachments": [], "parameters": [], "start": 1746617501617, "name": "fixture: page", "stop": 1746617502044}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617502049, "name": "page.goto(https://opensource-demo.orangehrmlive.com/web/index.php/auth/login)", "stop": 1746617510143}], "attachments": [], "parameters": [], "start": 1746617500599, "name": "Create user", "stop": 1746617510143}], "attachments": [], "parameters": [], "start": 1746617500597, "name": "Before Hooks", "stop": 1746617510143}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617510150, "name": "expect.toBeVisible", "stop": 1746617513077}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617513078, "name": "expect.toBeVisible", "stop": 1746617513085}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617513086, "name": "expect.toBeVisible", "stop": 1746617513093}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617513095, "name": "locator.fill([name = 'username'])", "stop": 1746617513119}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617513120, "name": "locator.fill([name='password'])", "stop": 1746617513137}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617513139, "name": "locator.getByRole('button', { name: 'Login', exact: true }).click", "stop": 1746617515024}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617515025, "name": "page.waitForTimeout", "stop": 1746617518037}, {"status": "failed", "statusDetails": {"message": "Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: getByRole('heading', { name: 'Cockpit', exact: true })\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for getByRole('heading', { name: 'Cockpit', exact: true })\n", "trace": "\n    at Object.enterLoginDetails (D:\\Playwright\\tests\\pages\\loginPage.ts:46:36)\n    at D:\\Playwright\\tests\\test.spec.ts:20:9"}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617518038, "name": "expect.toBeVisible", "stop": 1746617523052}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617523053, "name": "Delete user", "stop": 1746617523054}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617523054, "name": "fixture: page", "stop": 1746617523054}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617523054, "name": "fixture: context", "stop": 1746617523054}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617523092, "name": "fixture: browser", "stop": 1746617523309}], "attachments": [], "parameters": [], "start": 1746617523053, "name": "After Hooks", "stop": 1746617523310}], "attachments": [{"name": "stdout", "type": "text/plain", "source": "86939f0e-b97f-4076-a7ad-703ba95bcd1e-attachment.txt"}], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-17792-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}], "links": [], "start": 1746617500593, "name": "TC-01, <PERSON>gin to the URL", "fullName": "test.spec.ts#Login Tasks TC-01, Login to the URL", "testCaseId": "83e406c4843f1161094e4aa8d6cb36b7", "stop": 1746617523319}