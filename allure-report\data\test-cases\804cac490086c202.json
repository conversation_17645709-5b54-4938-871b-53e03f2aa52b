{"uid": "804cac490086c202", "name": "TC-01, <PERSON>gin to the URL", "fullName": "test.spec.ts#Login Tasks TC-01, Login to the URL", "historyId": "83e406c4843f1161094e4aa8d6cb36b7:5bd835b0d6b1d4ada3b9f0db936e82c8", "time": {"start": 1746617403244, "stop": 1746617425019, "duration": 21775}, "status": "failed", "statusMessage": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: locator('[name = \\'username\\']')\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('[name = \\'username\\']')\n", "statusTrace": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: locator('[name = \\'username\\']')\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('[name = \\'username\\']')\n\n    at Object.seeTheLoginPage (D:\\Playwright\\tests\\pages\\loginPage.ts:23:34)\n    at D:\\Playwright\\tests\\test.spec.ts:19:25", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "failed", "statusMessage": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: locator('[name = \\'username\\']')\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('[name = \\'username\\']')\n", "statusTrace": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: locator('[name = \\'username\\']')\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('[name = \\'username\\']')\n\n    at Object.seeTheLoginPage (D:\\Playwright\\tests\\pages\\loginPage.ts:23:34)\n    at D:\\Playwright\\tests\\test.spec.ts:19:25", "steps": [{"name": "Before Hooks", "time": {"start": 1746617403248, "stop": 1746617419691, "duration": 16443}, "status": "passed", "steps": [{"name": "Create user", "time": {"start": 1746617403250, "stop": 1746617419691, "duration": 16441}, "status": "passed", "steps": [{"name": "fixture: browser", "time": {"start": 1746617403274, "stop": 1746617407598, "duration": 4324}, "status": "passed", "steps": [{"name": "browserType.launch", "time": {"start": 1746617403278, "stop": 1746617407598, "duration": 4320}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: context", "time": {"start": 1746617407601, "stop": 1746617407620, "duration": 19}, "status": "passed", "steps": [{"name": "browser.newContext", "time": {"start": 1746617407604, "stop": 1746617407617, "duration": 13}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: page", "time": {"start": 1746617407621, "stop": 1746617408033, "duration": 412}, "status": "passed", "steps": [{"name": "browserContext.newPage", "time": {"start": 1746617407624, "stop": 1746617408033, "duration": 409}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "page.goto(https://opensource-demo.orangehrmlive.com/web/index.php/auth/login)", "time": {"start": 1746617408040, "stop": 1746617419691, "duration": 11651}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 7, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 8, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617419701, "stop": 1746617424727, "duration": 5026}, "status": "failed", "statusMessage": "Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: locator('[name = \\'username\\']')\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('[name = \\'username\\']')\n", "statusTrace": "\n    at Object.seeTheLoginPage (D:\\Playwright\\tests\\pages\\loginPage.ts:23:34)\n    at D:\\Playwright\\tests\\test.spec.ts:19:25", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": true}, {"name": "After Hooks", "time": {"start": 1746617424728, "stop": 1746617425012, "duration": 284}, "status": "passed", "steps": [{"name": "Delete user", "time": {"start": 1746617424728, "stop": 1746617424728, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: page", "time": {"start": 1746617424729, "stop": 1746617424729, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: context", "time": {"start": 1746617424729, "stop": 1746617424729, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: browser", "time": {"start": 1746617424772, "stop": 1746617425012, "duration": 240}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 4, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [{"uid": "7b1697eefdb027ff", "name": "stdout", "source": "7b1697eefdb027ff.txt", "type": "text/plain", "size": 73}], "parameters": [], "stepsCount": 15, "attachmentStep": false, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": true}, "afterStages": [], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-19640-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "Project", "value": "chromium"}], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "804cac490086c202.json", "parameterValues": ["chromium"]}