{"uuid": "1492b39b-b8c9-4fde-abde-e1cd0f21ca2d", "historyId": "83e406c4843f1161094e4aa8d6cb36b7:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617674768, "name": "browserType.launch", "stop": 1746617675664}], "attachments": [], "parameters": [], "start": 1746617674765, "name": "fixture: browser", "stop": 1746617675664}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617675669, "name": "browser.newContext", "stop": 1746617675686}], "attachments": [], "parameters": [], "start": 1746617675666, "name": "fixture: context", "stop": 1746617675689}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617675694, "name": "browserContext.newPage", "stop": 1746617676100}], "attachments": [], "parameters": [], "start": 1746617675691, "name": "fixture: page", "stop": 1746617676100}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617676105, "name": "page.goto(https://opensource-demo.orangehrmlive.com/web/index.php/auth/login)", "stop": 1746617682173}], "attachments": [], "parameters": [], "start": 1746617674740, "name": "Create user", "stop": 1746617682173}], "attachments": [], "parameters": [], "start": 1746617674738, "name": "Before Hooks", "stop": 1746617682174}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617682179, "name": "expect.toBeVisible", "stop": 1746617685131}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617685133, "name": "expect.toBeVisible", "stop": 1746617685142}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617685144, "name": "expect.toBeVisible", "stop": 1746617685155}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617685158, "name": "locator.fill([name = 'username'])", "stop": 1746617685180}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617685181, "name": "locator.fill([name='password'])", "stop": 1746617685207}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617685208, "name": "locator.getByRole('button', { name: 'Login', exact: true }).click", "stop": 1746617686899}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617686901, "name": "page.waitForTimeout", "stop": 1746617689903}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617689904, "name": "expect.toBeVisible", "stop": 1746617689922}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617689924, "name": "Delete user", "stop": 1746617689926}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617689927, "name": "fixture: page", "stop": 1746617689927}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617689928, "name": "fixture: context", "stop": 1746617689928}], "attachments": [], "parameters": [], "start": 1746617689922, "name": "After Hooks", "stop": 1746617689968}], "attachments": [{"name": "stdout", "type": "text/plain", "source": "9311750a-a7cd-4ba4-aede-2d2a2436aaea-attachment.txt"}], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-17532-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}], "links": [], "start": 1746617674735, "name": "TC-01, <PERSON>gin to the URL", "fullName": "test.spec.ts#Login Tasks TC-01, Login to the URL", "testCaseId": "83e406c4843f1161094e4aa8d6cb36b7", "stop": 1746617689973}