{"uid": "345aedcb9097894e", "name": "TC-01, <PERSON>gin to the URL", "fullName": "test.spec.ts#Login Tasks TC-01, Login to the URL", "historyId": "83e406c4843f1161094e4aa8d6cb36b7:5bd835b0d6b1d4ada3b9f0db936e82c8", "time": {"start": 1746617500593, "stop": 1746617523319, "duration": 22726}, "status": "failed", "statusMessage": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: getByRole('heading', { name: 'Cockpit', exact: true })\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for getByRole('heading', { name: 'Cockpit', exact: true })\n", "statusTrace": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: getByRole('heading', { name: 'Cockpit', exact: true })\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for getBy<PERSON>ole('heading', { name: 'Cockpit', exact: true })\n\n    at Object.enterLoginDetails (D:\\Playwright\\tests\\pages\\loginPage.ts:46:36)\n    at D:\\Playwright\\tests\\test.spec.ts:20:9", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "failed", "statusMessage": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: getByRole('heading', { name: 'Cockpit', exact: true })\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for getByRole('heading', { name: 'Cockpit', exact: true })\n", "statusTrace": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: getByRole('heading', { name: 'Cockpit', exact: true })\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for getBy<PERSON>ole('heading', { name: 'Cockpit', exact: true })\n\n    at Object.enterLoginDetails (D:\\Playwright\\tests\\pages\\loginPage.ts:46:36)\n    at D:\\Playwright\\tests\\test.spec.ts:20:9", "steps": [{"name": "Before Hooks", "time": {"start": 1746617500597, "stop": 1746617510143, "duration": 9546}, "status": "passed", "steps": [{"name": "Create user", "time": {"start": 1746617500599, "stop": 1746617510143, "duration": 9544}, "status": "passed", "steps": [{"name": "fixture: browser", "time": {"start": 1746617500625, "stop": 1746617501586, "duration": 961}, "status": "passed", "steps": [{"name": "browserType.launch", "time": {"start": 1746617500628, "stop": 1746617501586, "duration": 958}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: context", "time": {"start": 1746617501589, "stop": 1746617501616, "duration": 27}, "status": "passed", "steps": [{"name": "browser.newContext", "time": {"start": 1746617501591, "stop": 1746617501609, "duration": 18}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: page", "time": {"start": 1746617501617, "stop": 1746617502044, "duration": 427}, "status": "passed", "steps": [{"name": "browserContext.newPage", "time": {"start": 1746617501620, "stop": 1746617502044, "duration": 424}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "page.goto(https://opensource-demo.orangehrmlive.com/web/index.php/auth/login)", "time": {"start": 1746617502049, "stop": 1746617510143, "duration": 8094}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 7, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 8, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617510150, "stop": 1746617513077, "duration": 2927}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617513078, "stop": 1746617513085, "duration": 7}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617513086, "stop": 1746617513093, "duration": 7}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "locator.fill([name = 'username'])", "time": {"start": 1746617513095, "stop": 1746617513119, "duration": 24}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "locator.fill([name='password'])", "time": {"start": 1746617513120, "stop": 1746617513137, "duration": 17}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "locator.getByRole('button', { name: 'Login', exact: true }).click", "time": {"start": 1746617513139, "stop": 1746617515024, "duration": 1885}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "page.waitForTimeout", "time": {"start": 1746617515025, "stop": 1746617518037, "duration": 3012}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617518038, "stop": 1746617523052, "duration": 5014}, "status": "failed", "statusMessage": "Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: getByRole('heading', { name: 'Cockpit', exact: true })\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for getByRole('heading', { name: 'Cockpit', exact: true })\n", "statusTrace": "\n    at Object.enterLoginDetails (D:\\Playwright\\tests\\pages\\loginPage.ts:46:36)\n    at D:\\Playwright\\tests\\test.spec.ts:20:9", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": true}, {"name": "After Hooks", "time": {"start": 1746617523053, "stop": 1746617523310, "duration": 257}, "status": "passed", "steps": [{"name": "Delete user", "time": {"start": 1746617523053, "stop": 1746617523054, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: page", "time": {"start": 1746617523054, "stop": 1746617523054, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: context", "time": {"start": 1746617523054, "stop": 1746617523054, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: browser", "time": {"start": 1746617523092, "stop": 1746617523309, "duration": 217}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 4, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [{"uid": "8bf0910fc63c1b60", "name": "stdout", "source": "8bf0910fc63c1b60.txt", "type": "text/plain", "size": 73}], "parameters": [], "stepsCount": 22, "attachmentStep": false, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": true}, "afterStages": [], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-17792-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "Project", "value": "chromium"}], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "345aedcb9097894e.json", "parameterValues": ["chromium"]}