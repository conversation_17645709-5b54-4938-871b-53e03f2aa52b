{"uuid": "dc62e14b-0761-47ea-afce-4cf53467bac5", "historyId": "83e406c4843f1161094e4aa8d6cb36b7:84e28e814b821ed013329cc8dbc467e0", "status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028224526, "name": "browserType.launch", "stop": 1746028226829}], "attachments": [], "parameters": [], "start": 1746028224521, "name": "fixture: browser", "stop": 1746028226829}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028226834, "name": "browser.newContext", "stop": 1746028226879}], "attachments": [], "parameters": [], "start": 1746028226832, "name": "fixture: context", "stop": 1746028226882}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028226887, "name": "browserContext.newPage", "stop": 1746028227760}], "attachments": [], "parameters": [], "start": 1746028226883, "name": "fixture: page", "stop": 1746028227760}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028227820, "name": "page.goto(https://opensource-demo.orangehrmlive.com/web/index.php/auth/login)", "stop": 1746028234271}], "attachments": [], "parameters": [], "start": 1746028224441, "name": "Create user", "stop": 1746028234271}], "attachments": [], "parameters": [], "start": 1746028224437, "name": "Before Hooks", "stop": 1746028234271}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028234277, "name": "expect.toBeVisible", "stop": 1746028237217}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028237219, "name": "expect.toBeVisible", "stop": 1746028237227}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028237229, "name": "expect.toBeVisible", "stop": 1746028237245}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028237249, "name": "locator.fill([name = 'username'])", "stop": 1746028237287}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028237289, "name": "locator.fill([name='password'])", "stop": 1746028237317}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028237319, "name": "locator.getByRole('button', { name: 'Login', exact: true }).click", "stop": 1746028238666}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028238667, "name": "page.waitForTimeout", "stop": 1746028241670}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028241672, "name": "expect.toBeVisible", "stop": 1746028241694}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028241696, "name": "Delete user", "stop": 1746028241697}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028241697, "name": "fixture: page", "stop": 1746028241697}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028241697, "name": "fixture: context", "stop": 1746028241697}], "attachments": [], "parameters": [], "start": 1746028241694, "name": "After Hooks", "stop": 1746028241779}], "attachments": [{"name": "stdout", "type": "text/plain", "source": "df44aeee-b830-4da3-a58c-c75ea68bc189-attachment.txt"}], "parameters": [{"name": "Project", "value": "webkit"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > webkit > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-13624-playwright-worker-2"}, {"name": "parentSuite", "value": "webkit"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}], "links": [], "start": 1746028224431, "name": "TC-01, <PERSON>gin to the URL", "fullName": "test.spec.ts#Login Tasks TC-01, Login to the URL", "testCaseId": "83e406c4843f1161094e4aa8d6cb36b7", "stop": 1746028241781}