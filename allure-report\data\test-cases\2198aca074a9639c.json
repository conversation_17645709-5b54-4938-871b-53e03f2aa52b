{"uid": "2198aca074a9639c", "name": "TC-01, <PERSON>gin to the URL", "fullName": "test.spec.ts#Login Tasks TC-01, Login to the URL", "historyId": "83e406c4843f1161094e4aa8d6cb36b7:b444eb0fbe6390c71e68b51dd25701fc", "time": {"start": 1746028224404, "stop": 1746028241845, "duration": 17441}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Before Hooks", "time": {"start": 1746028224411, "stop": 1746028234259, "duration": 9848}, "status": "passed", "steps": [{"name": "Create user", "time": {"start": 1746028224414, "stop": 1746028234259, "duration": 9845}, "status": "passed", "steps": [{"name": "fixture: browser", "time": {"start": 1746028224452, "stop": 1746028227501, "duration": 3049}, "status": "passed", "steps": [{"name": "browserType.launch", "time": {"start": 1746028224459, "stop": 1746028227501, "duration": 3042}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: context", "time": {"start": 1746028227501, "stop": 1746028227502, "duration": 1}, "status": "passed", "steps": [{"name": "browser.newContext", "time": {"start": 1746028227501, "stop": 1746028227501, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: page", "time": {"start": 1746028227502, "stop": 1746028227935, "duration": 433}, "status": "passed", "steps": [{"name": "browserContext.newPage", "time": {"start": 1746028227502, "stop": 1746028227935, "duration": 433}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "page.goto(https://opensource-demo.orangehrmlive.com/web/index.php/auth/login)", "time": {"start": 1746028227960, "stop": 1746028234259, "duration": 6299}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 7, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 8, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746028234269, "stop": 1746028237235, "duration": 2966}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746028237238, "stop": 1746028237246, "duration": 8}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746028237248, "stop": 1746028237261, "duration": 13}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "locator.fill([name = 'username'])", "time": {"start": 1746028237268, "stop": 1746028237307, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "locator.fill([name='password'])", "time": {"start": 1746028237309, "stop": 1746028237332, "duration": 23}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "locator.getByRole('button', { name: 'Login', exact: true }).click", "time": {"start": 1746028237333, "stop": 1746028238680, "duration": 1347}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "page.waitForTimeout", "time": {"start": 1746028238682, "stop": 1746028241687, "duration": 3005}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746028241688, "stop": 1746028241716, "duration": 28}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "After Hooks", "time": {"start": 1746028241721, "stop": 1746028241844, "duration": 123}, "status": "passed", "steps": [{"name": "Delete user", "time": {"start": 1746028241723, "stop": 1746028241783, "duration": 60}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: page", "time": {"start": 1746028241783, "stop": 1746028241783, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: context", "time": {"start": 1746028241783, "stop": 1746028241783, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 3, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [{"uid": "4db8994c77b4c059", "name": "stdout", "source": "4db8994c77b4c059.txt", "type": "text/plain", "size": 73}], "parameters": [], "stepsCount": 21, "attachmentStep": false, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, "afterStages": [], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > firefox > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-13624-playwright-worker-1"}, {"name": "parentSuite", "value": "firefox"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "Project", "value": "firefox"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "2198aca074a9639c.json", "parameterValues": ["firefox"]}