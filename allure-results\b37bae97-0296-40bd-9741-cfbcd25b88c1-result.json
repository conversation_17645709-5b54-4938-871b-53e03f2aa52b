{"uuid": "b37bae97-0296-40bd-9741-cfbcd25b88c1", "historyId": "2c907fc9a8f1dc28613464738ffe75df:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617689997, "name": "browser.newContext", "stop": 1746617690005}], "attachments": [], "parameters": [], "start": 1746617689997, "name": "fixture: context", "stop": 1746617690007}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617690010, "name": "browserContext.newPage", "stop": 1746617690223}], "attachments": [], "parameters": [], "start": 1746617690008, "name": "fixture: page", "stop": 1746617690223}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617690225, "name": "page.goto(https://opensource-demo.orangehrmlive.com/web/index.php/auth/login)", "stop": 1746617695239}], "attachments": [], "parameters": [], "start": 1746617689980, "name": "Create user", "stop": 1746617695239}], "attachments": [], "parameters": [], "start": 1746617689980, "name": "Before Hooks", "stop": 1746617695239}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617695241, "name": "expect.toBeVisible", "stop": 1746617698169}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617698169, "name": "expect.toBeVisible", "stop": 1746617698171}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617698172, "name": "expect.toBeVisible", "stop": 1746617698177}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617698178, "name": "locator.fill([name = 'username'])", "stop": 1746617698193}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617698194, "name": "locator.fill([name='password'])", "stop": 1746617698207}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617698208, "name": "locator.getByRole('button', { name: 'Login', exact: true }).click", "stop": 1746617699929}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617699930, "name": "page.waitForTimeout", "stop": 1746617702932}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617702933, "name": "expect.toBeVisible", "stop": 1746617702950}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617702951, "name": "expect.toBeVisible", "stop": 1746617702956}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617702956, "name": "expect.toBeVisible", "stop": 1746617702959}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617702960, "name": "expect.toBeVisible", "stop": 1746617702962}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617702963, "name": "page.screenshot", "stop": 1746617703144}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617703149, "name": "Delete user", "stop": 1746617703150}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617703150, "name": "fixture: page", "stop": 1746617703150}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617703150, "name": "fixture: context", "stop": 1746617703150}], "attachments": [], "parameters": [], "start": 1746617703149, "name": "After Hooks", "stop": 1746617703184}], "attachments": [{"name": "stdout", "type": "text/plain", "source": "14b59f58-572a-40bc-8907-39945e5492e8-attachment.txt"}], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-17532-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}], "links": [], "start": 1746617689979, "name": "TC-02, Verify dashboard elements after login", "fullName": "test.spec.ts#Login Tasks TC-02, Verify dashboard elements after login", "testCaseId": "2c907fc9a8f1dc28613464738ffe75df", "stop": 1746617703187}