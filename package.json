{"devDependencies": {"@playwright/test": "^1.40.1", "@types/node": "^20.10.4", "allure-commandline": "^2.34.0", "allure-playwright": "^2.10.0", "axios": "^1.10.0", "dotenv": "^17.2.0", "playwright": "1.40.1", "ts-node": "^10.9.2"}, "scripts": {"test": "npx playwright test", "test:headed": "npx playwright test --headed", "test:debug": "npx playwright test --debug", "test:testrail": "TESTRAIL_ENABLED=true npx playwright test", "report": "npx playwright show-report", "install:browsers": "npx playwright install", "allure:generate": "allure generate allure-results -o allure-report --clean", "allure:open": "allure open allure-report", "testrail": "ts-node scripts/testRailManager.ts", "testrail:test-connection": "npm run testrail test-connection", "testrail:create-run": "npm run testrail create-run", "testrail:validate": "npm run testrail validate-mappings", "testrail:config": "npm run testrail show-config"}}