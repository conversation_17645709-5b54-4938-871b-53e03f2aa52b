{"uid": "6ca33fc5d63e62df", "name": "TC-02, Verify dashboard elements after login", "fullName": "test.spec.ts#Login Tasks TC-02, Verify dashboard elements after login", "historyId": "2c907fc9a8f1dc28613464738ffe75df:5bd835b0d6b1d4ada3b9f0db936e82c8", "time": {"start": 1746617574933, "stop": 1746617589508, "duration": 14575}, "status": "failed", "statusMessage": "Error: expect.toBeVisible: Error: strict mode violation: locator('.oxd-main-menu') resolved to 2 elements:\n    1) <div data-v-636d6b87=\"\" class=\"oxd-main-menu --fixed\">…</div> aka getByLabel('Sidepanel').locator('div').filter({ hasText: 'AdminPIMLeaveTimeRecruitmentMy' }).locator('div').first()\n    2) <ul data-v-636d6b87=\"\" class=\"oxd-main-menu\">…</ul> aka getByText('AdminPIMLeaveTimeRecruitmentMy')\n\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('.oxd-main-menu')\n", "statusTrace": "Error: expect.toBeVisible: Error: strict mode violation: locator('.oxd-main-menu') resolved to 2 elements:\n    1) <div data-v-636d6b87=\"\" class=\"oxd-main-menu --fixed\">…</div> aka getByLabel('Sidepanel').locator('div').filter({ hasText: 'AdminPIMLeaveTimeRecruitmentMy' }).locator('div').first()\n    2) <ul data-v-636d6b87=\"\" class=\"oxd-main-menu\">…</ul> aka getByText('AdminPIMLeaveTimeRecruitmentMy')\n\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('.oxd-main-menu')\n\n    at D:\\Playwright\\tests\\test.spec.ts:41:32", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "failed", "statusMessage": "Error: expect.toBeVisible: Error: strict mode violation: locator('.oxd-main-menu') resolved to 2 elements:\n    1) <div data-v-636d6b87=\"\" class=\"oxd-main-menu --fixed\">…</div> aka getByLabel('Sidepanel').locator('div').filter({ hasText: 'AdminPIMLeaveTimeRecruitmentMy' }).locator('div').first()\n    2) <ul data-v-636d6b87=\"\" class=\"oxd-main-menu\">…</ul> aka getByText('AdminPIMLeaveTimeRecruitmentMy')\n\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('.oxd-main-menu')\n", "statusTrace": "Error: expect.toBeVisible: Error: strict mode violation: locator('.oxd-main-menu') resolved to 2 elements:\n    1) <div data-v-636d6b87=\"\" class=\"oxd-main-menu --fixed\">…</div> aka getByLabel('Sidepanel').locator('div').filter({ hasText: 'AdminPIMLeaveTimeRecruitmentMy' }).locator('div').first()\n    2) <ul data-v-636d6b87=\"\" class=\"oxd-main-menu\">…</ul> aka getByText('AdminPIMLeaveTimeRecruitmentMy')\n\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('.oxd-main-menu')\n\n    at D:\\Playwright\\tests\\test.spec.ts:41:32", "steps": [{"name": "Before Hooks", "time": {"start": 1746617574934, "stop": 1746617580979, "duration": 6045}, "status": "passed", "steps": [{"name": "Create user", "time": {"start": 1746617574934, "stop": 1746617580979, "duration": 6045}, "status": "passed", "steps": [{"name": "fixture: context", "time": {"start": 1746617574943, "stop": 1746617574956, "duration": 13}, "status": "passed", "steps": [{"name": "browser.newContext", "time": {"start": 1746617574947, "stop": 1746617574955, "duration": 8}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: page", "time": {"start": 1746617574957, "stop": 1746617575215, "duration": 258}, "status": "passed", "steps": [{"name": "browserContext.newPage", "time": {"start": 1746617574960, "stop": 1746617575215, "duration": 255}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "page.goto(https://opensource-demo.orangehrmlive.com/web/index.php/auth/login)", "time": {"start": 1746617575216, "stop": 1746617580979, "duration": 5763}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 5, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 6, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617580981, "stop": 1746617583917, "duration": 2936}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617583918, "stop": 1746617583923, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617583924, "stop": 1746617583931, "duration": 7}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "locator.fill([name = 'username'])", "time": {"start": 1746617583932, "stop": 1746617583947, "duration": 15}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "locator.fill([name='password'])", "time": {"start": 1746617583948, "stop": 1746617583962, "duration": 14}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "locator.getByRole('button', { name: 'Login', exact: true }).click", "time": {"start": 1746617583963, "stop": 1746617586147, "duration": 2184}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "page.waitForTimeout", "time": {"start": 1746617586148, "stop": 1746617589152, "duration": 3004}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617589154, "stop": 1746617589171, "duration": 17}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617589172, "stop": 1746617589176, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617589177, "stop": 1746617589181, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617589181, "stop": 1746617589205, "duration": 24}, "status": "failed", "statusMessage": "expect.toBeVisible: Error: strict mode violation: locator('.oxd-main-menu') resolved to 2 elements:\n    1) <div data-v-636d6b87=\"\" class=\"oxd-main-menu --fixed\">…</div> aka getByLabel('Sidepanel').locator('div').filter({ hasText: 'AdminPIMLeaveTimeRecruitmentMy' }).locator('div').first()\n    2) <ul data-v-636d6b87=\"\" class=\"oxd-main-menu\">…</ul> aka getByText('AdminPIMLeaveTimeRecruitmentMy')\n\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('.oxd-main-menu')\n", "statusTrace": "\n    at D:\\Playwright\\tests\\test.spec.ts:41:32", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": true}, {"name": "After Hooks", "time": {"start": 1746617589206, "stop": 1746617589502, "duration": 296}, "status": "passed", "steps": [{"name": "Delete user", "time": {"start": 1746617589206, "stop": 1746617589207, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: page", "time": {"start": 1746617589207, "stop": 1746617589207, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: context", "time": {"start": 1746617589207, "stop": 1746617589207, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 3, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [{"uid": "6788b338b19ba033", "name": "stdout", "source": "6788b338b19ba033.txt", "type": "text/plain", "size": 115}], "parameters": [], "stepsCount": 22, "attachmentStep": false, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": true}, "afterStages": [], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-6492-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "Project", "value": "chromium"}], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "6ca33fc5d63e62df.json", "parameterValues": ["chromium"]}