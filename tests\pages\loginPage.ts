import { expect, Locator, Page } from '@playwright/test';


type LoginPage = {
    seeTheLoginPage(
    page: Page
  ): Promise<void>;
  enterLoginDetails(page: Page): Promise<void>;
  enterInvalidLoginDetails(page: Page, username: string, password: string): Promise<void>;
  verifyErrorMessage(page: Page, expectedMessage?: string): Promise<void>;
  clearLoginFields(page: Page): Promise<void>;
  getLoginElements(page: Page): Promise<{
    emailField: Locator;
    passwordField: Locator;
    loginBtn: Locator;
  }>;
};

const loginPage: LoginPage = {
  async seeTheLoginPage(
    page: Page,
    ): Promise<void> {
        let emailField: Locator = page.locator("[name = 'username']");
        let passwordField: Locator = page.locator("[name='password']");
        let loginBtn: Locator = page.getByRole('button', {
            name: "Login",
            exact: true,
        });
    

        await expect(emailField).toBeVisible();
        await expect(passwordField).toBeVisible();
        await expect(loginBtn).toBeVisible();
    },

  async enterLoginDetails(page: Page): Promise<void> {
        let emailField: Locator = page.locator("[name = 'username']");
        let passwordField: Locator = page.locator("[name='password']");
        let loginBtn: Locator = page.getByRole('button', {
            name: "Login",
            exact: true,
        });

        await emailField.fill("Admin");
        await passwordField.fill("admin123");
        await loginBtn.click();
        await page.waitForTimeout(3000)

        let dashboardTitle: Locator = page.getByRole('heading', {
          name: "Dashboard",
          exact: true,
      });
      
      await expect(dashboardTitle).toBeVisible();
    },

    async enterInvalidLoginDetails(page: Page, username: string, password: string): Promise<void> {
        let emailField: Locator = page.locator("[name = 'username']");
        let passwordField: Locator = page.locator("[name='password']");
        let loginBtn: Locator = page.getByRole('button', {
            name: "Login",
            exact: true,
        });

        // Clear fields first
        await emailField.clear();
        await passwordField.clear();

        // Enter provided credentials
        if (username) {
            await emailField.fill(username);
        }
        if (password) {
            await passwordField.fill(password);
        }

        await loginBtn.click();

        // Wait for either navigation or error message to appear
        try {
            await Promise.race([
                page.waitForURL('**/dashboard/**', { timeout: 5000 }),
                page.waitForSelector('.oxd-alert-content-text', { timeout: 5000 }),
                page.waitForSelector('.oxd-input-field-error-message', { timeout: 5000 }),
                page.waitForTimeout(3000) // Fallback timeout
            ]);
        } catch (error) {
            // Continue - some scenarios might not show immediate feedback
            console.log('No immediate feedback detected, continuing...');
        }
    },

    async verifyErrorMessage(page: Page, expectedMessage?: string): Promise<void> {
        // Common error message selectors for OrangeHRM
        const errorSelectors = [
            '.oxd-alert-content-text',
            '.oxd-text--toast-message',
            '.oxd-input-field-error-message',
            '[role="alert"]',
            '.error-message',
            '.alert-danger'
        ];

        let errorFound = false;
        let actualErrorMessage = '';

        // Wait a bit longer for error messages to appear
        await page.waitForTimeout(3000);

        // Try to find error message using different selectors
        for (const selector of errorSelectors) {
            try {
                const errorElement = page.locator(selector);
                const count = await errorElement.count();
                if (count > 0) {
                    // Check each element if multiple exist
                    for (let i = 0; i < count; i++) {
                        const element = errorElement.nth(i);
                        if (await element.isVisible()) {
                            actualErrorMessage = await element.textContent() || '';
                            actualErrorMessage = actualErrorMessage.trim();
                            if (actualErrorMessage) {
                                errorFound = true;
                                console.log(`Error message found with selector "${selector}": ${actualErrorMessage}`);
                                break;
                            }
                        }
                    }
                    if (errorFound) break;
                }
            } catch (error) {
                // Continue to next selector
            }
        }

        // If no specific selector worked, try generic approach
        if (!errorFound) {
            try {
                const genericError = page.locator('text=/invalid|error|incorrect|wrong|failed|required/i').first();
                if (await genericError.isVisible()) {
                    actualErrorMessage = await genericError.textContent() || '';
                    actualErrorMessage = actualErrorMessage.trim();
                    errorFound = true;
                    console.log(`Error message found with generic selector: ${actualErrorMessage}`);
                }
            } catch (error) {
                // No error message found
            }
        }

        // Additional check for form validation states
        if (!errorFound) {
            try {
                // Check if we're still on login page (indicating validation prevented submission)
                const loginButton = page.locator('button[type="submit"]');
                const isOnLoginPage = await loginButton.isVisible();

                if (isOnLoginPage) {
                    // Check for HTML5 validation or field highlighting
                    const usernameField = page.locator('[name="username"]');
                    const passwordField = page.locator('[name="password"]');

                    const usernameInvalid = await usernameField.evaluate(el => (el as HTMLInputElement).validity?.valid === false);
                    const passwordInvalid = await passwordField.evaluate(el => (el as HTMLInputElement).validity?.valid === false);

                    if (usernameInvalid || passwordInvalid) {
                        errorFound = true;
                        actualErrorMessage = 'Form validation prevented submission';
                        console.log('Form validation error detected');
                    }
                }
            } catch (error) {
                // Continue
            }
        }

        expect(errorFound).toBe(true);

        if (expectedMessage) {
            expect(actualErrorMessage.toLowerCase()).toContain(expectedMessage.toLowerCase());
        }

        console.log(`Verified error message: ${actualErrorMessage}`);
    },

    async clearLoginFields(page: Page): Promise<void> {
        try {
            // Wait for fields to be available before clearing
            const emailField: Locator = page.locator("[name = 'username']");
            const passwordField: Locator = page.locator("[name='password']");

            // Wait for fields to be visible and interactable
            await emailField.waitFor({ state: 'visible', timeout: 15000 });
            await passwordField.waitFor({ state: 'visible', timeout: 15000 });

            // Clear fields with retry mechanism
            try {
                await emailField.clear({ timeout: 10000 });
            } catch (error) {
                console.log('Retrying username field clear...');
                await emailField.fill('');
            }

            try {
                await passwordField.clear({ timeout: 10000 });
            } catch (error) {
                console.log('Retrying password field clear...');
                await passwordField.fill('');
            }

            console.log('Login fields cleared');
        } catch (error) {
            console.log('Error clearing login fields, but continuing...', error);
            // Don't throw error, just continue
        }
    },

    async getLoginElements(page: Page): Promise<{
        emailField: Locator;
        passwordField: Locator;
        loginBtn: Locator;
    }> {
        return {
            emailField: page.locator("[name = 'username']"),
            passwordField: page.locator("[name='password']"),
            loginBtn: page.getByRole('button', {
                name: "Login",
                exact: true,
            })
        };
    }

};

export default loginPage;
