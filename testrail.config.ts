/**
 * TestRail Integration Configuration
 * 
 * This file contains all configuration settings for TestRail integration
 * including API endpoints, authentication, and mapping configurations.
 */

export interface TestRailConfig {
  // TestRail instance configuration
  baseUrl: string;
  username: string;
  apiKey: string;
  
  // Project and suite configuration
  projectId: number;
  suiteId: number;
  
  // Test run configuration
  runName?: string;
  runDescription?: string;
  milestoneId?: number;
  assignedToId?: number;
  
  // Integration settings
  enabled: boolean;
  createRunAutomatically: boolean;
  updateResultsAutomatically: boolean;
  includeScreenshots: boolean;
  includeErrorLogs: boolean;
  
  // Retry and timeout settings
  apiTimeout: number;
  maxRetries: number;
  retryDelay: number;
}

export const defaultTestRailConfig: TestRailConfig = {
  // TestRail instance (set via environment variables)
  baseUrl: process.env.TESTRAIL_BASE_URL || '',
  username: process.env.TESTRAIL_USERNAME || '',
  apiKey: process.env.TESTRAIL_API_KEY || '',
  
  // Project configuration (set via environment variables)
  projectId: parseInt(process.env.TESTRAIL_PROJECT_ID || '0'),
  suiteId: parseInt(process.env.TESTRAIL_SUITE_ID || '0'),
  
  // Test run configuration
  runName: process.env.TESTRAIL_RUN_NAME || `Automated Test Run - ${new Date().toISOString().split('T')[0]}`,
  runDescription: process.env.TESTRAIL_RUN_DESCRIPTION || 'Automated test execution via Playwright',
  milestoneId: parseInt(process.env.TESTRAIL_MILESTONE_ID || '0') || undefined,
  assignedToId: parseInt(process.env.TESTRAIL_ASSIGNED_TO_ID || '0') || undefined,
  
  // Integration settings
  enabled: process.env.TESTRAIL_ENABLED === 'true',
  createRunAutomatically: process.env.TESTRAIL_CREATE_RUN_AUTO !== 'false',
  updateResultsAutomatically: process.env.TESTRAIL_UPDATE_RESULTS_AUTO !== 'false',
  includeScreenshots: process.env.TESTRAIL_INCLUDE_SCREENSHOTS !== 'false',
  includeErrorLogs: process.env.TESTRAIL_INCLUDE_ERROR_LOGS !== 'false',
  
  // API settings
  apiTimeout: parseInt(process.env.TESTRAIL_API_TIMEOUT || '30000'),
  maxRetries: parseInt(process.env.TESTRAIL_MAX_RETRIES || '3'),
  retryDelay: parseInt(process.env.TESTRAIL_RETRY_DELAY || '1000'),
};

// TestRail status IDs (standard TestRail installation)
export const TestRailStatus = {
  PASSED: 1,
  BLOCKED: 2,
  UNTESTED: 3,
  RETEST: 4,
  FAILED: 5,
  SKIPPED: 6, // Custom status if available
} as const;

// TestRail priority IDs (standard TestRail installation)
export const TestRailPriority = {
  LOW: 1,
  MEDIUM: 2,
  HIGH: 3,
  CRITICAL: 4,
} as const;

// Test case mapping interface
export interface TestCaseMapping {
  testId: string;           // Playwright test ID (e.g., 'TC-01', 'TC-NEG-01')
  testRailCaseId: number;   // TestRail case ID
  title: string;            // Test case title
  priority?: number;        // TestRail priority ID
  automationType?: string;  // Type of automation (e.g., 'ui', 'api', 'integration')
  tags?: string[];          // Additional tags for categorization
}

// Default test case mappings for current test suite
export const defaultTestCaseMappings: TestCaseMapping[] = [
  // Basic login functionality tests
  {
    testId: 'TC-01',
    testRailCaseId: 1001, // Replace with actual TestRail case ID
    title: 'Basic login functionality',
    priority: TestRailPriority.HIGH,
    automationType: 'ui',
    tags: ['login', 'positive', 'smoke']
  },
  {
    testId: 'TC-02',
    testRailCaseId: 1002,
    title: 'Dashboard elements verification',
    priority: TestRailPriority.MEDIUM,
    automationType: 'ui',
    tags: ['dashboard', 'ui', 'verification']
  },
  {
    testId: 'TC-03',
    testRailCaseId: 1003,
    title: 'Common functions demo',
    priority: TestRailPriority.LOW,
    automationType: 'ui',
    tags: ['demo', 'functions']
  },
  
  // Negative login test cases
  {
    testId: 'TC-NEG-01',
    testRailCaseId: 2001,
    title: 'Login with invalid username and valid password',
    priority: TestRailPriority.HIGH,
    automationType: 'ui',
    tags: ['login', 'negative', 'security']
  },
  {
    testId: 'TC-NEG-02',
    testRailCaseId: 2002,
    title: 'Login with valid username and invalid password',
    priority: TestRailPriority.HIGH,
    automationType: 'ui',
    tags: ['login', 'negative', 'security']
  },
  {
    testId: 'TC-NEG-03',
    testRailCaseId: 2003,
    title: 'Login with both invalid credentials',
    priority: TestRailPriority.HIGH,
    automationType: 'ui',
    tags: ['login', 'negative', 'security']
  },
  {
    testId: 'TC-NEG-04',
    testRailCaseId: 2004,
    title: 'Login with empty username field',
    priority: TestRailPriority.MEDIUM,
    automationType: 'ui',
    tags: ['login', 'negative', 'validation']
  },
  {
    testId: 'TC-NEG-05',
    testRailCaseId: 2005,
    title: 'Login with empty password field',
    priority: TestRailPriority.MEDIUM,
    automationType: 'ui',
    tags: ['login', 'negative', 'validation']
  },
  {
    testId: 'TC-NEG-06',
    testRailCaseId: 2006,
    title: 'Login with both empty fields',
    priority: TestRailPriority.MEDIUM,
    automationType: 'ui',
    tags: ['login', 'negative', 'validation']
  },
  {
    testId: 'TC-NEG-07',
    testRailCaseId: 2007,
    title: 'SQL injection attempts',
    priority: TestRailPriority.CRITICAL,
    automationType: 'ui',
    tags: ['login', 'negative', 'security', 'sql-injection']
  },
  {
    testId: 'TC-NEG-08',
    testRailCaseId: 2008,
    title: 'Special characters and XSS prevention',
    priority: TestRailPriority.CRITICAL,
    automationType: 'ui',
    tags: ['login', 'negative', 'security', 'xss']
  },
  {
    testId: 'TC-NEG-09',
    testRailCaseId: 2009,
    title: 'Very long credentials validation',
    priority: TestRailPriority.MEDIUM,
    automationType: 'ui',
    tags: ['login', 'negative', 'validation', 'boundary']
  },
  {
    testId: 'TC-NEG-10',
    testRailCaseId: 2010,
    title: 'Case sensitivity test',
    priority: TestRailPriority.MEDIUM,
    automationType: 'ui',
    tags: ['login', 'negative', 'case-sensitivity']
  },
  {
    testId: 'TC-NEG-11',
    testRailCaseId: 2011,
    title: 'Multiple rapid login attempts',
    priority: TestRailPriority.HIGH,
    automationType: 'ui',
    tags: ['login', 'negative', 'security', 'rate-limiting']
  },
  {
    testId: 'TC-NEG-12',
    testRailCaseId: 2012,
    title: 'Unicode and international characters',
    priority: TestRailPriority.MEDIUM,
    automationType: 'ui',
    tags: ['login', 'negative', 'unicode', 'internationalization']
  }
];

// Validation function for configuration
export function validateTestRailConfig(config: TestRailConfig): string[] {
  const errors: string[] = [];
  
  if (!config.baseUrl) {
    errors.push('TestRail base URL is required (TESTRAIL_BASE_URL)');
  }
  
  if (!config.username) {
    errors.push('TestRail username is required (TESTRAIL_USERNAME)');
  }
  
  if (!config.apiKey) {
    errors.push('TestRail API key is required (TESTRAIL_API_KEY)');
  }
  
  if (!config.projectId || config.projectId <= 0) {
    errors.push('Valid TestRail project ID is required (TESTRAIL_PROJECT_ID)');
  }
  
  if (!config.suiteId || config.suiteId <= 0) {
    errors.push('Valid TestRail suite ID is required (TESTRAIL_SUITE_ID)');
  }
  
  return errors;
}
