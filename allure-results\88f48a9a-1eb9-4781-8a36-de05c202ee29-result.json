{"uuid": "88f48a9a-1eb9-4781-8a36-de05c202ee29", "historyId": "2c907fc9a8f1dc28613464738ffe75df:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "failed", "statusDetails": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('.oxd-main-menu') resolved to 2 elements:\n    1) <div data-v-636d6b87=\"\" class=\"oxd-main-menu --fixed\">…</div> aka getByLabel('Sidepanel').locator('div').filter({ hasText: 'AdminPIMLeaveTimeRecruitmentMy' }).locator('div').first()\n    2) <ul data-v-636d6b87=\"\" class=\"oxd-main-menu\">…</ul> aka getByText('AdminPIMLeaveTimeRecruitmentMy')\n\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('.oxd-main-menu')\n", "trace": "Error: expect.toBeVisible: Error: strict mode violation: locator('.oxd-main-menu') resolved to 2 elements:\n    1) <div data-v-636d6b87=\"\" class=\"oxd-main-menu --fixed\">…</div> aka getByLabel('Sidepanel').locator('div').filter({ hasText: 'AdminPIMLeaveTimeRecruitmentMy' }).locator('div').first()\n    2) <ul data-v-636d6b87=\"\" class=\"oxd-main-menu\">…</ul> aka getByText('AdminPIMLeaveTimeRecruitmentMy')\n\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('.oxd-main-menu')\n\n    at D:\\Playwright\\tests\\test.spec.ts:41:32"}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617574947, "name": "browser.newContext", "stop": 1746617574955}], "attachments": [], "parameters": [], "start": 1746617574943, "name": "fixture: context", "stop": 1746617574956}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617574960, "name": "browserContext.newPage", "stop": 1746617575215}], "attachments": [], "parameters": [], "start": 1746617574957, "name": "fixture: page", "stop": 1746617575215}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617575216, "name": "page.goto(https://opensource-demo.orangehrmlive.com/web/index.php/auth/login)", "stop": 1746617580979}], "attachments": [], "parameters": [], "start": 1746617574934, "name": "Create user", "stop": 1746617580979}], "attachments": [], "parameters": [], "start": 1746617574934, "name": "Before Hooks", "stop": 1746617580979}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617580981, "name": "expect.toBeVisible", "stop": 1746617583917}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617583918, "name": "expect.toBeVisible", "stop": 1746617583923}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617583924, "name": "expect.toBeVisible", "stop": 1746617583931}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617583932, "name": "locator.fill([name = 'username'])", "stop": 1746617583947}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617583948, "name": "locator.fill([name='password'])", "stop": 1746617583962}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617583963, "name": "locator.getByRole('button', { name: 'Login', exact: true }).click", "stop": 1746617586147}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617586148, "name": "page.waitForTimeout", "stop": 1746617589152}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617589154, "name": "expect.toBeVisible", "stop": 1746617589171}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617589172, "name": "expect.toBeVisible", "stop": 1746617589176}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617589177, "name": "expect.toBeVisible", "stop": 1746617589181}, {"status": "failed", "statusDetails": {"message": "expect.toBeVisible: Error: strict mode violation: locator('.oxd-main-menu') resolved to 2 elements:\n    1) <div data-v-636d6b87=\"\" class=\"oxd-main-menu --fixed\">…</div> aka getByLabel('Sidepanel').locator('div').filter({ hasText: 'AdminPIMLeaveTimeRecruitmentMy' }).locator('div').first()\n    2) <ul data-v-636d6b87=\"\" class=\"oxd-main-menu\">…</ul> aka getByText('AdminPIMLeaveTimeRecruitmentMy')\n\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('.oxd-main-menu')\n", "trace": "\n    at D:\\Playwright\\tests\\test.spec.ts:41:32"}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617589181, "name": "expect.toBeVisible", "stop": 1746617589205}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617589206, "name": "Delete user", "stop": 1746617589207}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617589207, "name": "fixture: page", "stop": 1746617589207}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617589207, "name": "fixture: context", "stop": 1746617589207}], "attachments": [], "parameters": [], "start": 1746617589206, "name": "After Hooks", "stop": 1746617589502}], "attachments": [{"name": "stdout", "type": "text/plain", "source": "5a2ec66b-1f52-4793-91d5-54fc5d3c59e5-attachment.txt"}], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-6492-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}], "links": [], "start": 1746617574933, "name": "TC-02, Verify dashboard elements after login", "fullName": "test.spec.ts#Login Tasks TC-02, Verify dashboard elements after login", "testCaseId": "2c907fc9a8f1dc28613464738ffe75df", "stop": 1746617589508}