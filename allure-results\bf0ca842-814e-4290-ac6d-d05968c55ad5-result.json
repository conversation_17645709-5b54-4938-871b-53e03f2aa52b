{"uuid": "bf0ca842-814e-4290-ac6d-d05968c55ad5", "historyId": "83e406c4843f1161094e4aa8d6cb36b7:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "failed", "statusDetails": {}, "stage": "pending", "steps": [{"statusDetails": {}, "stage": "pending", "steps": [{"statusDetails": {}, "stage": "pending", "steps": [{"statusDetails": {}, "stage": "pending", "steps": [{"statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028296393, "name": "browserType.launch"}], "attachments": [], "parameters": [], "start": 1746028296389, "name": "fixture: browser"}], "attachments": [], "parameters": [], "start": 1746028296358, "name": "Create user"}], "attachments": [], "parameters": [], "start": 1746028296356, "name": "Before Hooks"}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028296865, "name": "Delete user", "stop": 1746028296910}], "attachments": [], "parameters": [], "start": 1746028296863, "name": "After Hooks", "stop": 1746028296910}], "attachments": [{"name": "stdout", "type": "text/plain", "source": "e2901074-2e99-41dd-9932-23376b7ddcd8-attachment.txt"}], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-18896-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}], "links": [], "start": 1746028296353, "name": "TC-01, <PERSON>gin to the URL", "fullName": "test.spec.ts#Login Tasks TC-01, Login to the URL", "testCaseId": "83e406c4843f1161094e4aa8d6cb36b7", "stop": 1746028297117}