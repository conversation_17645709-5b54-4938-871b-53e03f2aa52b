{"uid": "2f4918ca54974d04", "name": "TC-02, Verify dashboard elements after login", "fullName": "test.spec.ts#Login Tasks TC-02, Verify dashboard elements after login", "historyId": "2c907fc9a8f1dc28613464738ffe75df:5bd835b0d6b1d4ada3b9f0db936e82c8", "time": {"start": 1746617425460, "stop": 1746617441123, "duration": 15663}, "status": "failed", "statusMessage": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: locator('[name = \\'username\\']')\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('[name = \\'username\\']')\n", "statusTrace": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: locator('[name = \\'username\\']')\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('[name = \\'username\\']')\n\n    at Object.seeTheLoginPage (D:\\Playwright\\tests\\pages\\loginPage.ts:23:34)\n    at D:\\Playwright\\tests\\test.spec.ts:25:25", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "failed", "statusMessage": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: locator('[name = \\'username\\']')\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('[name = \\'username\\']')\n", "statusTrace": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: locator('[name = \\'username\\']')\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('[name = \\'username\\']')\n\n    at Object.seeTheLoginPage (D:\\Playwright\\tests\\pages\\loginPage.ts:23:34)\n    at D:\\Playwright\\tests\\test.spec.ts:25:25", "steps": [{"name": "Before Hooks", "time": {"start": 1746617425466, "stop": 1746617435861, "duration": 10395}, "status": "passed", "steps": [{"name": "Create user", "time": {"start": 1746617425467, "stop": 1746617435860, "duration": 10393}, "status": "passed", "steps": [{"name": "fixture: browser", "time": {"start": 1746617425490, "stop": 1746617426505, "duration": 1015}, "status": "passed", "steps": [{"name": "browserType.launch", "time": {"start": 1746617425495, "stop": 1746617426505, "duration": 1010}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: context", "time": {"start": 1746617426508, "stop": 1746617426534, "duration": 26}, "status": "passed", "steps": [{"name": "browser.newContext", "time": {"start": 1746617426510, "stop": 1746617426530, "duration": 20}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: page", "time": {"start": 1746617426535, "stop": 1746617426934, "duration": 399}, "status": "passed", "steps": [{"name": "browserContext.newPage", "time": {"start": 1746617426538, "stop": 1746617426934, "duration": 396}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "page.goto(https://opensource-demo.orangehrmlive.com/web/index.php/auth/login)", "time": {"start": 1746617426939, "stop": 1746617435860, "duration": 8921}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 7, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 8, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "expect.toBeVisible", "time": {"start": 1746617435866, "stop": 1746617440874, "duration": 5008}, "status": "failed", "statusMessage": "Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: locator('[name = \\'username\\']')\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('[name = \\'username\\']')\n", "statusTrace": "\n    at Object.seeTheLoginPage (D:\\Playwright\\tests\\pages\\loginPage.ts:23:34)\n    at D:\\Playwright\\tests\\test.spec.ts:25:25", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": true}, {"name": "After Hooks", "time": {"start": 1746617440874, "stop": 1746617441117, "duration": 243}, "status": "passed", "steps": [{"name": "Delete user", "time": {"start": 1746617440874, "stop": 1746617440875, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: page", "time": {"start": 1746617440875, "stop": 1746617440875, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: context", "time": {"start": 1746617440876, "stop": 1746617440876, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "fixture: browser", "time": {"start": 1746617440917, "stop": 1746617441117, "duration": 200}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 4, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [{"uid": "7b380c903626a4b2", "name": "stdout", "source": "7b380c903626a4b2.txt", "type": "text/plain", "size": 115}], "parameters": [], "stepsCount": 15, "attachmentStep": false, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": true}, "afterStages": [], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-19640-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "Project", "value": "chromium"}], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "2f4918ca54974d04.json", "parameterValues": ["chromium"]}