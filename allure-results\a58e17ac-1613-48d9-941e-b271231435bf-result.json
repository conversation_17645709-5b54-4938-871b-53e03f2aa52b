{"uuid": "a58e17ac-1613-48d9-941e-b271231435bf", "historyId": "83e406c4843f1161094e4aa8d6cb36b7:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617558914, "name": "browserType.launch", "stop": 1746617560185}], "attachments": [], "parameters": [], "start": 1746617558910, "name": "fixture: browser", "stop": 1746617560185}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617560193, "name": "browser.newContext", "stop": 1746617560212}], "attachments": [], "parameters": [], "start": 1746617560189, "name": "fixture: context", "stop": 1746617560217}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617560223, "name": "browserContext.newPage", "stop": 1746617560840}], "attachments": [], "parameters": [], "start": 1746617560219, "name": "fixture: page", "stop": 1746617560841}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617560848, "name": "page.goto(https://opensource-demo.orangehrmlive.com/web/index.php/auth/login)", "stop": 1746617566617}], "attachments": [], "parameters": [], "start": 1746617558884, "name": "Create user", "stop": 1746617566617}], "attachments": [], "parameters": [], "start": 1746617558882, "name": "Before Hooks", "stop": 1746617566618}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617566624, "name": "expect.toBeVisible", "stop": 1746617569581}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617569582, "name": "expect.toBeVisible", "stop": 1746617569586}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617569587, "name": "expect.toBeVisible", "stop": 1746617569595}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617569596, "name": "locator.fill([name = 'username'])", "stop": 1746617569620}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617569621, "name": "locator.fill([name='password'])", "stop": 1746617569639}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617569640, "name": "locator.getByRole('button', { name: 'Login', exact: true }).click", "stop": 1746617571849}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617571850, "name": "page.waitForTimeout", "stop": 1746617574856}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617574857, "name": "expect.toBeVisible", "stop": 1746617574890}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617574891, "name": "Delete user", "stop": 1746617574892}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617574892, "name": "fixture: page", "stop": 1746617574892}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617574893, "name": "fixture: context", "stop": 1746617574893}], "attachments": [], "parameters": [], "start": 1746617574890, "name": "After Hooks", "stop": 1746617574925}], "attachments": [{"name": "stdout", "type": "text/plain", "source": "43a82ed8-cf89-49e2-85d2-c2c788e22aa1-attachment.txt"}], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-6492-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}], "links": [], "start": 1746617558878, "name": "TC-01, <PERSON>gin to the URL", "fullName": "test.spec.ts#Login Tasks TC-01, Login to the URL", "testCaseId": "83e406c4843f1161094e4aa8d6cb36b7", "stop": 1746617574929}