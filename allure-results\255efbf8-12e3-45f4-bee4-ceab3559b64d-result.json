{"uuid": "255efbf8-12e3-45f4-bee4-ceab3559b64d", "historyId": "83e406c4843f1161094e4aa8d6cb36b7:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028224419, "name": "browserType.launch", "stop": 1746028226400}], "attachments": [], "parameters": [], "start": 1746028224415, "name": "fixture: browser", "stop": 1746028226400}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028226405, "name": "browser.newContext", "stop": 1746028226419}], "attachments": [], "parameters": [], "start": 1746028226402, "name": "fixture: context", "stop": 1746028226422}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028226425, "name": "browserContext.newPage", "stop": 1746028226999}], "attachments": [], "parameters": [], "start": 1746028226423, "name": "fixture: page", "stop": 1746028226999}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028227502, "name": "page.goto(https://opensource-demo.orangehrmlive.com/web/index.php/auth/login)", "stop": 1746028234338}], "attachments": [], "parameters": [], "start": 1746028224383, "name": "Create user", "stop": 1746028234338}], "attachments": [], "parameters": [], "start": 1746028224381, "name": "Before Hooks", "stop": 1746028234339}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028234350, "name": "expect.toBeVisible", "stop": 1746028236458}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028236458, "name": "expect.toBeVisible", "stop": 1746028236482}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028236483, "name": "expect.toBeVisible", "stop": 1746028236499}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028236504, "name": "locator.fill([name = 'username'])", "stop": 1746028236550}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028236552, "name": "locator.fill([name='password'])", "stop": 1746028236588}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028236590, "name": "locator.getByRole('button', { name: 'Login', exact: true }).click", "stop": 1746028238553}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028238556, "name": "page.waitForTimeout", "stop": 1746028241561}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028241563, "name": "expect.toBeVisible", "stop": 1746028241603}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028241605, "name": "Delete user", "stop": 1746028241607}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028241607, "name": "fixture: page", "stop": 1746028241607}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746028241607, "name": "fixture: context", "stop": 1746028241607}], "attachments": [], "parameters": [], "start": 1746028241604, "name": "After Hooks", "stop": 1746028241662}], "attachments": [{"name": "stdout", "type": "text/plain", "source": "1853df0e-4bdf-490b-94d2-cde22f7e167a-attachment.txt"}], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-13624-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}], "links": [], "start": 1746028224376, "name": "TC-01, <PERSON>gin to the URL", "fullName": "test.spec.ts#Login Tasks TC-01, Login to the URL", "testCaseId": "83e406c4843f1161094e4aa8d6cb36b7", "stop": 1746028241665}