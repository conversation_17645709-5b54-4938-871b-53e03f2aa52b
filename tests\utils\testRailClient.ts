/**
 * TestRail API Client
 * 
 * This client handles all communication with TestRail API including:
 * - Creating test runs
 * - Adding test results
 * - Uploading attachments
 * - Managing test cases
 */

import axios, { AxiosInstance } from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import { TestRailConfig } from '../../testrail.config';

export interface TestRailCase {
  id: number;
  title: string;
  section_id: number;
  template_id: number;
  type_id: number;
  priority_id: number;
  milestone_id?: number;
  refs?: string;
  created_on: number;
  created_by: number;
  updated_on: number;
  updated_by: number;
  suite_id: number;
  custom_automation_type?: number;
  custom_preconds?: string;
  custom_steps?: string;
  custom_expected?: string;
}

export interface TestRailRun {
  id: number;
  suite_id: number;
  name: string;
  description?: string;
  milestone_id?: number;
  assignedto_id?: number;
  include_all: boolean;
  case_ids?: number[];
  is_completed: boolean;
  completed_on?: number;
  passed_count: number;
  blocked_count: number;
  untested_count: number;
  retest_count: number;
  failed_count: number;
  custom_status1_count?: number;
  custom_status2_count?: number;
  custom_status3_count?: number;
  custom_status4_count?: number;
  custom_status5_count?: number;
  custom_status6_count?: number;
  custom_status7_count?: number;
  project_id: number;
  plan_id?: number;
  created_on: number;
  created_by: number;
  url: string;
}

export interface TestRailResult {
  test_id: number;
  status_id: number;
  comment?: string;
  version?: string;
  elapsed?: string;
  defects?: string;
  assignedto_id?: number;
  custom_step_results?: Array<{
    content: string;
    expected: string;
    actual: string;
    status_id: number;
  }>;
}

export interface TestRailTest {
  id: number;
  case_id: number;
  status_id: number;
  assignedto_id?: number;
  run_id: number;
  title: string;
  template_id: number;
  type_id: number;
  priority_id: number;
  estimate?: string;
  estimate_forecast?: string;
  refs?: string;
  milestone_id?: number;
  custom_automation_type?: number;
}

export class TestRailClient {
  private client: AxiosInstance;
  private config: TestRailConfig;

  constructor(config: TestRailConfig) {
    this.config = config;
    
    // Create axios instance with authentication
    this.client = axios.create({
      baseURL: `${config.baseUrl}/index.php?/api/v2`,
      timeout: config.apiTimeout,
      auth: {
        username: config.username,
        password: config.apiKey
      },
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Playwright-TestRail-Integration/1.0'
      }
    });

    // Add request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        console.log(`[TestRail API] ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('[TestRail API] Request error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => {
        console.log(`[TestRail API] Response: ${response.status} ${response.statusText}`);
        return response;
      },
      (error) => {
        console.error('[TestRail API] Response error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  /**
   * Test the connection to TestRail
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.client.get('/get_user_by_email', {
        params: { email: this.config.username }
      });
      console.log('[TestRail] Connection test successful');
      return true;
    } catch (error) {
      console.error('[TestRail] Connection test failed:', error);
      return false;
    }
  }

  /**
   * Get project information
   */
  async getProject(): Promise<any> {
    try {
      const response = await this.client.get(`/get_project/${this.config.projectId}`);
      return response.data;
    } catch (error) {
      console.error('[TestRail] Failed to get project:', error);
      throw error;
    }
  }

  /**
   * Get test cases from a suite
   */
  async getCases(suiteId?: number): Promise<TestRailCase[]> {
    try {
      const targetSuiteId = suiteId || this.config.suiteId;
      const response = await this.client.get(`/get_cases/${this.config.projectId}`, {
        params: { suite_id: targetSuiteId }
      });
      return response.data;
    } catch (error) {
      console.error('[TestRail] Failed to get cases:', error);
      throw error;
    }
  }

  /**
   * Create a new test run
   */
  async createRun(name: string, description?: string, caseIds?: number[]): Promise<TestRailRun> {
    try {
      const runData = {
        suite_id: this.config.suiteId,
        name: name,
        description: description || this.config.runDescription,
        milestone_id: this.config.milestoneId,
        assignedto_id: this.config.assignedToId,
        include_all: !caseIds || caseIds.length === 0,
        case_ids: caseIds && caseIds.length > 0 ? caseIds : undefined
      };

      const response = await this.client.post(`/add_run/${this.config.projectId}`, runData);
      console.log(`[TestRail] Created test run: ${response.data.id} - ${response.data.name}`);
      return response.data;
    } catch (error) {
      console.error('[TestRail] Failed to create run:', error);
      throw error;
    }
  }

  /**
   * Get tests from a run
   */
  async getTests(runId: number): Promise<TestRailTest[]> {
    try {
      const response = await this.client.get(`/get_tests/${runId}`);
      return response.data;
    } catch (error) {
      console.error('[TestRail] Failed to get tests:', error);
      throw error;
    }
  }

  /**
   * Add a test result
   */
  async addResult(testId: number, statusId: number, comment?: string, elapsed?: string): Promise<any> {
    try {
      const resultData: TestRailResult = {
        test_id: testId,
        status_id: statusId,
        comment: comment,
        elapsed: elapsed,
        version: process.env.npm_package_version || '1.0.0'
      };

      const response = await this.client.post(`/add_result/${testId}`, resultData);
      console.log(`[TestRail] Added result for test ${testId}: Status ${statusId}`);
      return response.data;
    } catch (error) {
      console.error('[TestRail] Failed to add result:', error);
      throw error;
    }
  }

  /**
   * Add multiple test results
   */
  async addResults(runId: number, results: TestRailResult[]): Promise<any> {
    try {
      const response = await this.client.post(`/add_results/${runId}`, { results });
      console.log(`[TestRail] Added ${results.length} results to run ${runId}`);
      return response.data;
    } catch (error) {
      console.error('[TestRail] Failed to add results:', error);
      throw error;
    }
  }

  /**
   * Upload an attachment to a test result
   */
  async addAttachment(resultId: number, filePath: string): Promise<any> {
    try {
      if (!fs.existsSync(filePath)) {
        console.warn(`[TestRail] Attachment file not found: ${filePath}`);
        return null;
      }

      const formData = new FormData();
      const fileBuffer = fs.readFileSync(filePath);
      const fileName = path.basename(filePath);
      
      // Create a Blob from the buffer
      const blob = new Blob([fileBuffer]);
      formData.append('attachment', blob, fileName);

      const response = await this.client.post(`/add_attachment_to_result/${resultId}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      console.log(`[TestRail] Uploaded attachment: ${fileName}`);
      return response.data;
    } catch (error) {
      console.error('[TestRail] Failed to upload attachment:', error);
      throw error;
    }
  }

  /**
   * Close a test run
   */
  async closeRun(runId: number): Promise<any> {
    try {
      const response = await this.client.post(`/close_run/${runId}`);
      console.log(`[TestRail] Closed test run: ${runId}`);
      return response.data;
    } catch (error) {
      console.error('[TestRail] Failed to close run:', error);
      throw error;
    }
  }

  /**
   * Get run information
   */
  async getRun(runId: number): Promise<TestRailRun> {
    try {
      const response = await this.client.get(`/get_run/${runId}`);
      return response.data;
    } catch (error) {
      console.error('[TestRail] Failed to get run:', error);
      throw error;
    }
  }

  /**
   * Update a test run
   */
  async updateRun(runId: number, data: Partial<TestRailRun>): Promise<TestRailRun> {
    try {
      const response = await this.client.post(`/update_run/${runId}`, data);
      console.log(`[TestRail] Updated test run: ${runId}`);
      return response.data;
    } catch (error) {
      console.error('[TestRail] Failed to update run:', error);
      throw error;
    }
  }

  /**
   * Retry mechanism for API calls
   */
  private async retryApiCall<T>(apiCall: () => Promise<T>, maxRetries: number = this.config.maxRetries): Promise<T> {
    let lastError: any;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await apiCall();
      } catch (error) {
        lastError = error;
        console.warn(`[TestRail] API call failed (attempt ${attempt}/${maxRetries}):`, error);
        
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay * attempt));
        }
      }
    }
    
    throw lastError;
  }
}
