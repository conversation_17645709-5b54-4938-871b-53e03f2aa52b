{"uuid": "8179f8ee-7ea9-426c-9864-0691e8d662f8", "historyId": "83e406c4843f1161094e4aa8d6cb36b7:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "failed", "statusDetails": {"message": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: locator('[name = \\'username\\']')\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('[name = \\'username\\']')\n", "trace": "Error: Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: locator('[name = \\'username\\']')\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('[name = \\'username\\']')\n\n    at Object.seeTheLoginPage (D:\\Playwright\\tests\\pages\\loginPage.ts:23:34)\n    at D:\\Playwright\\tests\\test.spec.ts:19:25"}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617403278, "name": "browserType.launch", "stop": 1746617407598}], "attachments": [], "parameters": [], "start": 1746617403274, "name": "fixture: browser", "stop": 1746617407598}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617407604, "name": "browser.newContext", "stop": 1746617407617}], "attachments": [], "parameters": [], "start": 1746617407601, "name": "fixture: context", "stop": 1746617407620}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617407624, "name": "browserContext.newPage", "stop": 1746617408033}], "attachments": [], "parameters": [], "start": 1746617407621, "name": "fixture: page", "stop": 1746617408033}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617408040, "name": "page.goto(https://opensource-demo.orangehrmlive.com/web/index.php/auth/login)", "stop": 1746617419691}], "attachments": [], "parameters": [], "start": 1746617403250, "name": "Create user", "stop": 1746617419691}], "attachments": [], "parameters": [], "start": 1746617403248, "name": "Before Hooks", "stop": 1746617419691}, {"status": "failed", "statusDetails": {"message": "Timed out 5000ms waiting for expect(locator).toBeVisible()\n\nLocator: locator('[name = \\'username\\']')\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('[name = \\'username\\']')\n", "trace": "\n    at Object.seeTheLoginPage (D:\\Playwright\\tests\\pages\\loginPage.ts:23:34)\n    at D:\\Playwright\\tests\\test.spec.ts:19:25"}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617419701, "name": "expect.toBeVisible", "stop": 1746617424727}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617424728, "name": "Delete user", "stop": 1746617424728}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617424729, "name": "fixture: page", "stop": 1746617424729}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617424729, "name": "fixture: context", "stop": 1746617424729}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1746617424772, "name": "fixture: browser", "stop": 1746617425012}], "attachments": [], "parameters": [], "start": 1746617424728, "name": "After Hooks", "stop": 1746617425012}], "attachments": [{"name": "stdout", "type": "text/plain", "source": "cd588590-5397-4387-a65f-02e4919ec174-attachment.txt"}], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-19640-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}], "links": [], "start": 1746617403244, "name": "TC-01, <PERSON>gin to the URL", "fullName": "test.spec.ts#Login Tasks TC-01, Login to the URL", "testCaseId": "83e406c4843f1161094e4aa8d6cb36b7", "stop": 1746617425019}