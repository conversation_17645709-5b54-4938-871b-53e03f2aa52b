# TestRail Integration Configuration
# Copy this file to .env and fill in your TestRail details

# TestRail Instance Configuration
TESTRAIL_BASE_URL=https://your-company.testrail.io
TESTRAIL_USERNAME=<EMAIL>
TESTRAIL_API_KEY=your-api-key-here

# Project and Suite Configuration
TESTRAIL_PROJECT_ID=1
TESTRAIL_SUITE_ID=1

# Test Run Configuration (Optional)
TESTRAIL_RUN_NAME=Automated Test Run - Playwright
TESTRAIL_RUN_DESCRIPTION=Automated test execution via Playwright framework
TESTRAIL_MILESTONE_ID=
TESTRAIL_ASSIGNED_TO_ID=

# Integration Settings
TESTRAIL_ENABLED=true
TESTRAIL_CREATE_RUN_AUTO=true
TESTRAIL_UPDATE_RESULTS_AUTO=true
TESTRAIL_INCLUDE_SCREENSHOTS=true
TESTRAIL_INCLUDE_ERROR_LOGS=true
TESTRAIL_CLOSE_RUN_AUTO=false

# API Settings
TESTRAIL_API_TIMEOUT=30000
TESTRAIL_MAX_RETRIES=3
TESTRAIL_RETRY_DELAY=1000

# Application Configuration (if needed)
npm_package_version=1.0.0
