import { Page, Locator, expect } from '@playwright/test';

/**
 * Common Functions for Playwright Test Automation
 * This file contains reusable functions for common web interactions
 */

type CommonFunctions = {
    // Text operations
    readText(page: Page, locator: string | Locator): Promise<string>;
    readTextContent(page: Page, locator: string | Locator): Promise<string>;
    readInnerText(page: Page, locator: string | Locator): Promise<string>;
    
    // Input operations
    enterText(page: Page, locator: string | Locator, text: string): Promise<void>;
    clearAndEnterText(page: Page, locator: string | Locator, text: string): Promise<void>;
    
    // Click operations
    clickElement(page: Page, locator: string | Locator): Promise<void>;
    doubleClickElement(page: Page, locator: string | Locator): Promise<void>;
    rightClickElement(page: Page, locator: string | Locator): Promise<void>;
    
    // Scroll operations
    scrollToElement(page: Page, locator: string | Locator): Promise<void>;
    scrollToElementAndWait(page: Page, locator: string | Locator, timeout?: number): Promise<void>;
    scrollDown(page: Page, pixels?: number): Promise<void>;
    scrollUp(page: Page, pixels?: number): Promise<void>;
    
    // Wait operations
    waitForElementVisible(page: Page, locator: string | Locator, timeout?: number): Promise<void>;
    waitForElementHidden(page: Page, locator: string | Locator, timeout?: number): Promise<void>;
    
    // Utility functions
    isElementVisible(page: Page, locator: string | Locator): Promise<boolean>;
    isElementEnabled(page: Page, locator: string | Locator): Promise<boolean>;
    getElementCount(page: Page, locator: string | Locator): Promise<number>;

    // Advanced utility functions (from testHelpers)
    waitForElement(page: Page, selector: string, timeout?: number): Promise<void>;
    takeScreenshot(page: Page, name: string): Promise<void>;
    checkAccessibility(page: Page): Promise<void>;
    measurePageLoad(page: Page, url: string): Promise<number>;
};

const commonFunctions: CommonFunctions = {
    /**
     * Read text from an element (gets textContent)
     * @param page - Playwright Page object
     * @param locator - Element locator (string or Locator object)
     * @returns Promise<string> - The text content of the element
     */
    async readText(page: Page, locator: string | Locator): Promise<string> {
        const element = typeof locator === 'string' ? page.locator(locator) : locator;
        await element.waitFor({ state: 'visible' });
        const text = await element.textContent();
        return text?.trim() || '';
    },

    /**
     * Read text content from an element (alternative method)
     * @param page - Playwright Page object
     * @param locator - Element locator (string or Locator object)
     * @returns Promise<string> - The text content of the element
     */
    async readTextContent(page: Page, locator: string | Locator): Promise<string> {
        const element = typeof locator === 'string' ? page.locator(locator) : locator;
        await element.waitFor({ state: 'visible' });
        const text = await element.textContent();
        return text?.trim() || '';
    },

    /**
     * Read inner text from an element
     * @param page - Playwright Page object
     * @param locator - Element locator (string or Locator object)
     * @returns Promise<string> - The inner text of the element
     */
    async readInnerText(page: Page, locator: string | Locator): Promise<string> {
        const element = typeof locator === 'string' ? page.locator(locator) : locator;
        await element.waitFor({ state: 'visible' });
        const text = await element.innerText();
        return text.trim();
    },

    /**
     * Enter text into an input field
     * @param page - Playwright Page object
     * @param locator - Element locator (string or Locator object)
     * @param text - Text to enter
     */
    async enterText(page: Page, locator: string | Locator, text: string): Promise<void> {
        const element = typeof locator === 'string' ? page.locator(locator) : locator;
        await element.waitFor({ state: 'visible' });
        await element.fill(text);
        console.log(`Entered text: "${text}" into element: ${locator}`);
    },

    /**
     * Clear existing text and enter new text into an input field
     * @param page - Playwright Page object
     * @param locator - Element locator (string or Locator object)
     * @param text - Text to enter
     */
    async clearAndEnterText(page: Page, locator: string | Locator, text: string): Promise<void> {
        const element = typeof locator === 'string' ? page.locator(locator) : locator;
        await element.waitFor({ state: 'visible' });
        await element.clear();
        await element.fill(text);
        console.log(`Cleared and entered text: "${text}" into element: ${locator}`);
    },

    /**
     * Click on an element
     * @param page - Playwright Page object
     * @param locator - Element locator (string or Locator object)
     */
    async clickElement(page: Page, locator: string | Locator): Promise<void> {
        const element = typeof locator === 'string' ? page.locator(locator) : locator;
        await element.waitFor({ state: 'visible' });
        await element.click();
        console.log(`Clicked on element: ${locator}`);
    },

    /**
     * Double click on an element
     * @param page - Playwright Page object
     * @param locator - Element locator (string or Locator object)
     */
    async doubleClickElement(page: Page, locator: string | Locator): Promise<void> {
        const element = typeof locator === 'string' ? page.locator(locator) : locator;
        await element.waitFor({ state: 'visible' });
        await element.dblclick();
        console.log(`Double clicked on element: ${locator}`);
    },

    /**
     * Right click on an element
     * @param page - Playwright Page object
     * @param locator - Element locator (string or Locator object)
     */
    async rightClickElement(page: Page, locator: string | Locator): Promise<void> {
        const element = typeof locator === 'string' ? page.locator(locator) : locator;
        await element.waitFor({ state: 'visible' });
        await element.click({ button: 'right' });
        console.log(`Right clicked on element: ${locator}`);
    },

    /**
     * Scroll to an element to make it visible
     * @param page - Playwright Page object
     * @param locator - Element locator (string or Locator object)
     */
    async scrollToElement(page: Page, locator: string | Locator): Promise<void> {
        const element = typeof locator === 'string' ? page.locator(locator) : locator;
        await element.scrollIntoViewIfNeeded();
        console.log(`Scrolled to element: ${locator}`);
    },

    /**
     * Scroll to an element and wait for it to be visible
     * @param page - Playwright Page object
     * @param locator - Element locator (string or Locator object)
     * @param timeout - Optional timeout in milliseconds (default: 5000)
     */
    async scrollToElementAndWait(page: Page, locator: string | Locator, timeout: number = 5000): Promise<void> {
        const element = typeof locator === 'string' ? page.locator(locator) : locator;
        await element.scrollIntoViewIfNeeded();
        await element.waitFor({ state: 'visible', timeout });
        console.log(`Scrolled to and waited for element: ${locator}`);
    },

    /**
     * Scroll down the page
     * @param page - Playwright Page object
     * @param pixels - Number of pixels to scroll (default: 500)
     */
    async scrollDown(page: Page, pixels: number = 500): Promise<void> {
        await page.evaluate((scrollPixels) => {
            window.scrollBy(0, scrollPixels);
        }, pixels);
        console.log(`Scrolled down by ${pixels} pixels`);
    },

    /**
     * Scroll up the page
     * @param page - Playwright Page object
     * @param pixels - Number of pixels to scroll (default: 500)
     */
    async scrollUp(page: Page, pixels: number = 500): Promise<void> {
        await page.evaluate((scrollPixels) => {
            window.scrollBy(0, -scrollPixels);
        }, pixels);
        console.log(`Scrolled up by ${pixels} pixels`);
    },

    /**
     * Wait for an element to be visible
     * @param page - Playwright Page object
     * @param locator - Element locator (string or Locator object)
     * @param timeout - Optional timeout in milliseconds (default: 5000)
     */
    async waitForElementVisible(page: Page, locator: string | Locator, timeout: number = 5000): Promise<void> {
        const element = typeof locator === 'string' ? page.locator(locator) : locator;
        await element.waitFor({ state: 'visible', timeout });
        console.log(`Element is now visible: ${locator}`);
    },

    /**
     * Wait for an element to be hidden
     * @param page - Playwright Page object
     * @param locator - Element locator (string or Locator object)
     * @param timeout - Optional timeout in milliseconds (default: 5000)
     */
    async waitForElementHidden(page: Page, locator: string | Locator, timeout: number = 5000): Promise<void> {
        const element = typeof locator === 'string' ? page.locator(locator) : locator;
        await element.waitFor({ state: 'hidden', timeout });
        console.log(`Element is now hidden: ${locator}`);
    },

    /**
     * Check if an element is visible
     * @param page - Playwright Page object
     * @param locator - Element locator (string or Locator object)
     * @returns Promise<boolean> - True if element is visible, false otherwise
     */
    async isElementVisible(page: Page, locator: string | Locator): Promise<boolean> {
        const element = typeof locator === 'string' ? page.locator(locator) : locator;
        try {
            await element.waitFor({ state: 'visible', timeout: 1000 });
            return true;
        } catch {
            return false;
        }
    },

    /**
     * Check if an element is enabled
     * @param page - Playwright Page object
     * @param locator - Element locator (string or Locator object)
     * @returns Promise<boolean> - True if element is enabled, false otherwise
     */
    async isElementEnabled(page: Page, locator: string | Locator): Promise<boolean> {
        const element = typeof locator === 'string' ? page.locator(locator) : locator;
        await element.waitFor({ state: 'visible' });
        return await element.isEnabled();
    },

    /**
     * Get the count of elements matching the locator
     * @param page - Playwright Page object
     * @param locator - Element locator (string or Locator object)
     * @returns Promise<number> - Number of matching elements
     */
    async getElementCount(page: Page, locator: string | Locator): Promise<number> {
        const element = typeof locator === 'string' ? page.locator(locator) : locator;
        return await element.count();
    },

    /**
     * Wait for element to be visible with custom timeout (from testHelpers)
     * @param page - Playwright Page object
     * @param selector - Element selector string
     * @param timeout - Optional timeout in milliseconds (default: 5000)
     */
    async waitForElement(page: Page, selector: string, timeout: number = 5000): Promise<void> {
        await page.waitForSelector(selector, { state: 'visible', timeout });
        console.log(`Element is now visible: ${selector}`);
    },

    /**
     * Take screenshot with timestamp (from testHelpers)
     * @param page - Playwright Page object
     * @param name - Base name for the screenshot file
     */
    async takeScreenshot(page: Page, name: string): Promise<void> {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        await page.screenshot({
            path: `screenshots/${name}-${timestamp}.png`,
            fullPage: true
        });
        console.log(`Screenshot taken: screenshots/${name}-${timestamp}.png`);
    },

    /**
     * Verify page accessibility (from testHelpers)
     * @param page - Playwright Page object
     */
    async checkAccessibility(page: Page): Promise<void> {
        // Check for basic accessibility requirements
        const headings = await page.locator('h1, h2, h3, h4, h5, h6').count();
        expect(headings).toBeGreaterThan(0);

        const images = await page.locator('img').count();
        if (images > 0) {
            const imagesWithAlt = await page.locator('img[alt]').count();
            expect(imagesWithAlt).toBe(images);
        }
        console.log('Accessibility check completed successfully');
    },

    /**
     * Performance monitoring (from testHelpers)
     * @param page - Playwright Page object
     * @param url - URL to navigate to and measure load time
     * @returns Promise<number> - Load time in milliseconds
     */
    async measurePageLoad(page: Page, url: string): Promise<number> {
        const startTime = Date.now();
        await page.goto(url);
        await page.waitForLoadState('networkidle');
        const loadTime = Date.now() - startTime;

        console.log(`Page load time: ${loadTime}ms`);
        expect(loadTime).toBeLessThan(10000); // Should load within 10 seconds

        return loadTime;
    }
};

export default commonFunctions;
