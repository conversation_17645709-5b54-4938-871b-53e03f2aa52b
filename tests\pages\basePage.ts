import { expect, Locator, Page } from '@playwright/test';

type BasePage = {
  loadUrl(
    page: Page
  ): Promise<void>;
  enterLoginDetails(page: Page): Promise<void>;
};

const basePage: BasePage = {
  async loadUrl(
    page: Page,
  ): Promise<void> {
    await page.goto("https://opensource-demo.orangehrmlive.com/web/index.php/auth/login");

  },

  async enterLoginDetails(page: Page): Promise<void> {
    await page.goto("https://opensource-demo.orangehrmlive.com/web/index.php/auth/login");
    const title = await page.title();
    console.log('Page title:', title);
    // await page.pause()

    await page.getByText('Login').click();

    // const frameLocator1 = page.frameLocator('#js-library-iframe'); //id of first iFrame
    // const framelocator2 = frameLocator1.frameLocator('[id="ds-signing-document"]') //id of second iFrame

    // await framelocator2.getByRole('button', {
    //     name: 'Agree and Continue'
    // }).click();

    // await framelocator2.getByRole('button',
    //     {name: 'Start Signing'}
    // ).click()
    // await page.pause();
  }
};

export default basePage;
