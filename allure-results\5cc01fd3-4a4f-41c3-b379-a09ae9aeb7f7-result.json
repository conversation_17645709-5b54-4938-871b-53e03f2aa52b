{"uuid": "5cc01fd3-4a4f-41c3-b379-a09ae9aeb7f7", "historyId": "0e8b58035f0104d4325344b3aeb04fe2:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "skipped", "statusDetails": {"message": "This test was skipped due to test setup error. Check you setup scripts to fix the issue."}, "stage": "pending", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > mobile.spec.ts"}, {"name": "ALLURE_ID", "value": "-1"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-3708-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "mobile.spec.ts"}], "links": [], "start": 1751830594422, "name": "Run in Android - Chrome", "fullName": "mobile.spec.ts#Run in Android - Chrome", "testCaseId": "0e8b58035f0104d4325344b3aeb04fe2", "stop": 1751830594430}