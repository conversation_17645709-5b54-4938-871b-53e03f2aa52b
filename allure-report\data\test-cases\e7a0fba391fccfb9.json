{"uid": "e7a0fba391fccfb9", "name": "TC-01, <PERSON>gin to the URL", "fullName": "test.spec.ts#Login Tasks TC-01, Login to the URL", "historyId": "83e406c4843f1161094e4aa8d6cb36b7:5bd835b0d6b1d4ada3b9f0db936e82c8", "time": {"start": 1746028296353, "stop": 1746028297117, "duration": 764}, "status": "failed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "failed", "steps": [{"name": "Before Hooks", "time": {"start": 1746028296356}, "status": "unknown", "steps": [{"name": "Create user", "time": {"start": 1746028296358}, "status": "unknown", "steps": [{"name": "fixture: browser", "time": {"start": 1746028296389}, "status": "unknown", "steps": [{"name": "browserType.launch", "time": {"start": 1746028296393}, "status": "unknown", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 2, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 3, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "After Hooks", "time": {"start": 1746028296863, "stop": 1746028296910, "duration": 47}, "status": "passed", "steps": [{"name": "Delete user", "time": {"start": 1746028296865, "stop": 1746028296910, "duration": 45}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "attachmentStep": false, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [], "parameters": [], "stepsCount": 1, "attachmentStep": false, "hasContent": true, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [{"uid": "3e0fa61768b9e49b", "name": "stdout", "source": "3e0fa61768b9e49b.txt", "type": "text/plain", "size": 36}], "parameters": [], "stepsCount": 6, "attachmentStep": false, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, "afterStages": [], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-18896-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "Project", "value": "chromium"}], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "e7a0fba391fccfb9.json", "parameterValues": ["chromium"]}