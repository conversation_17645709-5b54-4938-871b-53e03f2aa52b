# TestRail Integration for Playwright

This document describes the comprehensive TestRail integration for your Playwright test automation project. The integration automatically creates test runs, maps test cases, and synchronizes results with TestRail.

## 🚀 Features

- **Automatic Test Run Creation**: Creates TestRail runs automatically before test execution
- **Test Case Mapping**: Maps Playwright tests to TestRail test cases
- **Real-time Result Sync**: Updates TestRail with test results after execution
- **Screenshot & Log Upload**: Attaches screenshots and error logs to failed tests
- **Flexible Configuration**: Environment-based configuration with sensible defaults
- **CLI Management Tools**: Command-line utilities for TestRail operations
- **Retry Mechanisms**: Robust error handling with automatic retries

## 📋 Prerequisites

1. **TestRail Instance**: Access to a TestRail instance with API enabled
2. **API Key**: TestRail API key for authentication
3. **Project Setup**: TestRail project with test suites and cases

## ⚙️ Setup

### 1. Environment Configuration

Copy the example environment file and configure your TestRail settings:

```bash
cp .env.example .env
```

Edit `.env` with your TestRail details:

```env
# TestRail Instance Configuration
TESTRAIL_BASE_URL=https://your-company.testrail.io
TESTRAIL_USERNAME=<EMAIL>
TESTRAIL_API_KEY=your-api-key-here

# Project and Suite Configuration
TESTRAIL_PROJECT_ID=1
TESTRAIL_SUITE_ID=1

# Integration Settings
TESTRAIL_ENABLED=true
TESTRAIL_CREATE_RUN_AUTO=true
TESTRAIL_UPDATE_RESULTS_AUTO=true
TESTRAIL_INCLUDE_SCREENSHOTS=true
TESTRAIL_INCLUDE_ERROR_LOGS=true
```

### 2. Test Case Mapping

Update `testrail.config.ts` to map your Playwright tests to TestRail cases:

```typescript
export const defaultTestCaseMappings: TestCaseMapping[] = [
  {
    testId: 'TC-01',
    testRailCaseId: 1001, // Your TestRail case ID
    title: 'Basic login functionality',
    priority: TestRailPriority.HIGH,
    automationType: 'ui',
    tags: ['login', 'positive', 'smoke']
  },
  // Add more mappings...
];
```

### 3. Test Naming Convention

Ensure your Playwright tests follow the naming convention for automatic mapping:

```typescript
test('TC-01: Basic login functionality', async ({ page, testRailIntegration }) => {
  // Your test code here
});
```

## 🧪 Usage

### Running Tests with TestRail Integration

```bash
# Run tests with TestRail integration enabled
npm run test:testrail

# Run specific test file with TestRail
TESTRAIL_ENABLED=true npx playwright test tests/login.spec.ts

# Run tests without TestRail (default)
npm run test
```

### TestRail Management Commands

```bash
# Test connection to TestRail
npm run testrail:test-connection

# Create a new test run
npm run testrail:create-run

# Validate test case mappings
npm run testrail:validate

# Show current configuration
npm run testrail:config

# Show all available commands
npm run testrail help
```

### Advanced CLI Usage

```bash
# Create a custom test run
npm run testrail create-run "Sprint 1 Testing" "Testing new login features"

# List all test cases in the suite
npm run testrail list-cases

# Show current test case mappings
npm run testrail show-mappings

# Validate mappings against TestRail
npm run testrail validate-mappings
```

## 📊 Test Execution Flow

1. **Initialization**: TestRail integration initializes and tests connection
2. **Run Creation**: Automatically creates a new test run with mapped cases
3. **Test Execution**: Playwright tests run normally with TestRail hooks
4. **Result Collection**: Test results, screenshots, and logs are collected
5. **Result Upload**: Results are uploaded to TestRail with attachments
6. **Summary Report**: Execution summary is displayed and logged

## 🔧 Configuration Options

### TestRail Settings

| Setting | Environment Variable | Default | Description |
|---------|---------------------|---------|-------------|
| Base URL | `TESTRAIL_BASE_URL` | - | TestRail instance URL |
| Username | `TESTRAIL_USERNAME` | - | TestRail username/email |
| API Key | `TESTRAIL_API_KEY` | - | TestRail API key |
| Project ID | `TESTRAIL_PROJECT_ID` | - | TestRail project ID |
| Suite ID | `TESTRAIL_SUITE_ID` | - | TestRail suite ID |

### Integration Settings

| Setting | Environment Variable | Default | Description |
|---------|---------------------|---------|-------------|
| Enabled | `TESTRAIL_ENABLED` | `false` | Enable/disable integration |
| Auto Create Run | `TESTRAIL_CREATE_RUN_AUTO` | `true` | Auto-create test runs |
| Auto Update Results | `TESTRAIL_UPDATE_RESULTS_AUTO` | `true` | Auto-update results |
| Include Screenshots | `TESTRAIL_INCLUDE_SCREENSHOTS` | `true` | Upload screenshots |
| Include Error Logs | `TESTRAIL_INCLUDE_ERROR_LOGS` | `true` | Upload error logs |
| Close Run Auto | `TESTRAIL_CLOSE_RUN_AUTO` | `false` | Auto-close runs |

### API Settings

| Setting | Environment Variable | Default | Description |
|---------|---------------------|---------|-------------|
| API Timeout | `TESTRAIL_API_TIMEOUT` | `30000` | API timeout (ms) |
| Max Retries | `TESTRAIL_MAX_RETRIES` | `3` | Max retry attempts |
| Retry Delay | `TESTRAIL_RETRY_DELAY` | `1000` | Retry delay (ms) |

## 📝 Test Case Mapping

### Automatic Detection

The integration automatically detects test IDs from:

1. **Test Title**: `test('TC-01: Basic login functionality', ...)`
2. **File Name**: `TC-01-login.spec.ts`
3. **Annotations**: `test.annotate({ type: 'testId', description: 'TC-01' })`

### Manual Mapping

Configure mappings in `testrail.config.ts`:

```typescript
{
  testId: 'TC-NEG-01',
  testRailCaseId: 2001,
  title: 'Login with invalid credentials',
  priority: TestRailPriority.HIGH,
  automationType: 'ui',
  tags: ['login', 'negative', 'security']
}
```

## 📈 Result Mapping

| Playwright Status | TestRail Status | Description |
|------------------|-----------------|-------------|
| `passed` | Passed (1) | Test executed successfully |
| `failed` | Failed (5) | Test failed with errors |
| `skipped` | Skipped (6) | Test was skipped |
| `timedOut` | Failed (5) | Test timed out |

## 🔍 Troubleshooting

### Common Issues

1. **Connection Failed**
   ```bash
   npm run testrail:test-connection
   ```
   - Verify URL, username, and API key
   - Check network connectivity
   - Ensure API is enabled in TestRail

2. **Invalid Mappings**
   ```bash
   npm run testrail:validate
   ```
   - Update case IDs in `testrail.config.ts`
   - Verify cases exist in TestRail

3. **Tests Not Mapped**
   - Check test naming convention
   - Verify test ID extraction logic
   - Add manual mappings if needed

### Debug Mode

Enable debug logging:

```bash
DEBUG=testrail:* npm run test:testrail
```

### Disable Integration

Temporarily disable TestRail:

```bash
TESTRAIL_ENABLED=false npm run test
```

## 🔐 Security

- **API Keys**: Store in environment variables, never commit to code
- **Access Control**: Use dedicated TestRail user with minimal permissions
- **Network**: Ensure secure connection to TestRail instance

## 📚 API Reference

### TestRailClient

```typescript
const client = new TestRailClient(config);
await client.testConnection();
await client.createRun(name, description, caseIds);
await client.addResult(testId, statusId, comment);
```

### TestRailIntegration

```typescript
const integration = new TestRailIntegration(config, mappings);
await integration.initialize();
integration.onTestStart(testInfo);
integration.onTestEnd(testInfo, result);
await integration.finalizeResults();
```

## 🤝 Contributing

1. Update test case mappings in `testrail.config.ts`
2. Add new test cases following naming conventions
3. Test integration with `npm run testrail:validate`
4. Document any configuration changes

## 📞 Support

For issues and questions:

1. Check this documentation
2. Run diagnostic commands (`npm run testrail:test-connection`)
3. Review TestRail API documentation
4. Contact your TestRail administrator

---

**Happy Testing! 🧪✨**
